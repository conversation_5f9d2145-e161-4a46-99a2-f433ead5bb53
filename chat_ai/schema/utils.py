from django.utils.translation import gettext_lazy as _
from gabbro.graphene import BadRequest

from organizations.models import Organization
from workspaces.models import Workspace


def validate_workspace_and_get_workspace(
    input_workspace_id: int, organization: Organization
) -> Workspace:
    # Find the workspace associated with the provided workspace ID
    workspace = Workspace.objects.filter(
        organization=organization, id=input_workspace_id
    ).first()
    if not workspace:
        raise BadRequest(reason={"workspace_id": _("Invalid workspace_id") % {}})
    return workspace


def filter_layers(workspace: Workspace, layers_ids: list[int]):
    # Get all layers in the workspace
    if layers_ids:
        layers = workspace.layers.filter(id__in=layers_ids)
        if not layers:
            raise BadRequest(
                reason={"layers_ids": _("No layers found with the provided IDs") % {}}
            )
    else:
        layers = workspace.layers.all()
    return layers
