# Dynamic GIS Table Creation Solution

## Transforming GIS Data Storage for Enhanced Performance and Standards Compliance

---

## Executive Summary

### Current Challenge

Our GIS application currently stores all feature properties from imported files (GeoJSON, Geopackage, Shapefile) as JSON
data in a single `Record` table. This approach creates performance bottlenecks, limits query capabilities, and doesn't
follow GIS industry standards.

### Proposed Solution

Implement dynamic database table creation where each imported GIS layer gets its own dedicated table with individual
columns for each property. This approach will:

- **Improve query performance* through proper indexing
- **Comply with GIS industry standards** for data storage
- **Enhance GeoServer integration** capabilities

### Business Impact

- **Integration**: Improved compatibility with GIS tools
- **Maintenance**: Easier data management and analysis

---

## Current System Analysis

### How GIS Import Works Today

```mermaid
graph TD
    A[GIS File Upload] --> B[File Processing]
    B --> C[Extract Features]
    C --> D[Flatten Properties to JSON]
    D --> E[Store in Record Table]
    E --> F[Single JSON Column Storage]
    F --> G[Performance Issues]
    F --> H[Limited Query Capabilities]
    F --> I[Non-Standard Storage]
```

### Current Data Flow

1. **File Upload**: User uploads GIS file (GeoJSON, Geopackage, etc.)
2. **Processing**: `DatasetFilesLoader` extracts features and properties
3. **Storage**: All properties stored as JSON in `Record.source_properties`
4. **Querying**: Complex JSON path queries for filtering data

### Limitations of Current Approach

| Issue                 | Impact                                | Business Cost            |
|-----------------------|---------------------------------------|--------------------------|
| **Slow Queries**      | JSON path queries are 5-10x slower    | Poor user experience     |
| **No Indexing**       | Cannot index individual properties    | Scalability issues       |
| **Type Safety**       | All values stored as strings          | Data integrity problems  |
| **GIS Standards**     | Non-compliant with industry practices | Integration difficulties |
| **Complex Filtering** | Limited query capabilities            | Reduced functionality    |

---

## Proposed Solution: Dynamic Table Creation

### Core Concept

Instead of storing all GIS properties as JSON, create a dedicated database table for each imported layer with individual
columns for each property.

### New Data Flow

```mermaid
graph TD
    A[GIS File Upload] --> B[File Processing]
    B --> C[Extract Features]
    C --> D[Analyze Schema]
    D --> E[Create Django Model]
    E --> F[Create Dynamic Table With Property Fields]
    F --> G[Import Data to Columns]
    G --> K[High-Performance Queries]
```

### Dynamic Table Structure

```sql
-- Dynamic table for each layer
CREATE TABLE dynamic_layer_123_riyadh_districts
(
    id          SERIAL PRIMARY KEY,
    layer_id    INTEGER,
    geometry    GEOMETRY,
    created     TIMESTAMP,
    modified    TIMESTAMP,

    -- Dynamic columns based on GIS properties
    city        VARCHAR(100),
    population  INTEGER,
    area_sqkm   FLOAT,
    established DATE,
    poi_type    VARCHAR(50),

    -- Spatial index
    INDEX USING GIST (geometry)
);
```

### Key Benefits

#### 1. Performance Improvements

- **faster queries** through column indexing
- **Efficient spatial operations** with proper geometry indexing
- **Optimized memory usage** with appropriate data types

#### 2. GIS Standards Compliance

- **Industry-standard storage** patterns
- **Better GeoServer integration**
- **Compatible with standard GIS tools**

#### 3. Enhanced Functionality

- **Standard SQL operations** (GROUP BY, ORDER BY, aggregations)
- **Proper data types** (integers, dates, floats)
- **Advanced filtering** capabilities

---

## Technical Implementation Plan

### Phase 1: Foundation (Weeks 1-2)

**Goal**: Build core dynamic table creation infrastructure

#### 1.1 Schema Analysis Engine

#### 1.2 Dynamic Model Manager (execute queries)

### Phase 2: Import Process Integration

**Goal**: Modify existing import workflow to use dynamic tables

#### 2.1 Enhanced Importing Strategy

### Phase 3: GraphQL Integration

**Goal**: Update GraphQL resolvers to query dynamic tables

#### 3.1 Dynamic GraphQL Types

#### 3.2 Enhanced Query Resolvers

### Phase 4: Migration and Testing

**Goal**: Migrate existing data and comprehensive testing

#### 4.1 Data Migration Utility

```python
# New: layers/management/commands/migrate_to_dynamic_tables.py
class Command(BaseCommand):
    def handle(self, *args, **options):
        """Migrate existing JSON data to dynamic tables"""
        # For each layer with JSON data
        # Analyze existing properties
        # Create dynamic table
        # Migrate data with type conversion
```

---

## Risk Assessment and Mitigation

### Technical Risks

| Risk                          | Probability | Impact   | Mitigation Strategy                                |
|-------------------------------|-------------|----------|----------------------------------------------------|
| **Database Schema Conflicts** | Medium      | High     | Implement robust naming conventions and validation |
| **Migration Data Loss**       | Low         | Critical | Comprehensive backup strategy and rollback plan    |
| **Performance Regression**    | Low         | Medium   | Extensive testing and gradual rollout              |
| **GraphQL Breaking Changes**  | Medium      | Medium   | Maintain backward compatibility during transition  |

### Operational Risks

| Risk                        | Probability | Impact | Mitigation Strategy                           |
|-----------------------------|-------------|--------|-----------------------------------------------|
| **Increased Database Size** | High        | Low    | Monitor storage, implement archiving strategy |
| **Complex Maintenance**     | Medium      | Medium | Comprehensive documentation and training      |
| **Team Learning Curve**     | Medium      | Low    | Phased implementation with knowledge transfer |

---

## GraphQL Integration Details

### Current GraphQL Structure

```graphql
# Current query for records
query GetRecords($layerId: Int!) {
  records(layerId: $layerId) {
    data {
      id
      sourceProperties  # JSON blob
      mapData          # JSON blob
    }
  }
}
```

### Enhanced GraphQL Structure

```graphql
# New dynamic query with typed fields
query GetDynamicRecords($layerId: Int!) {
  dynamicRecords(layerId: $layerId) {
    data {
      id
      geometry
      # Typed fields based on layer schema
      city: String
      population: Int
      area_sqkm: Float
      established: Date
    }
  }
}
```

### Filtering Capabilities

```graphql
# Advanced filtering with proper types
query GetFilteredRecords($layerId: Int!, $filters: [FilterGroupInput!]) {
  dynamicRecords(layerId: $layerId, filterGroups: $filters) {
    count
    data {
      city
      population
    }
  }
}
```

### Backward Compatibility

- **Maintain existing `records` query** for legacy support
- **Gradual migration** of frontend applications

---

### Migration Tools

#### 1. Data Migration Command

```bash
# Migrate specific layers
python manage.py migrate_to_dynamic_tables --layer-ids 1,2,3

# Migrate all layers
python manage.py migrate_to_dynamic_tables --all

# Dry run for validation
python manage.py migrate_to_dynamic_tables --dry-run
```

### Rollback Strategy

- **Database snapshots** before each migration batch
- **Rollback scripts** for each phase

---
