from django.contrib.auth.models import Permission
from gabbro.acl.models import Role
from rest_framework import serializers


class RoleSerializer(serializers.ModelSerializer):
    permissions_list = serializers.ListField(
        child=serializers.PrimaryKeyRelatedField(
            queryset=Permission.objects.filter(content_type__model="organization")
        ),
        allow_empty=False,
    )

    class Meta:
        model = Role
        fields = ("id", "codename", "title", "permissions_list")
