from common.tests.schema.base_test import BaseTestCase


class UserDetailTestCase(BaseTestCase):
    maxDiff = None

    def setUp(self):
        super().setUp()
        self.query = """
        query UserDetails{
            userDetails{
                id
                firstName
                lastName
                email
                phone
                isSuperuser
                isStaff
                activeStatus
                avatar
            }
        }
        """

    def test_users_detail(self):
        result = self.client.execute(
            self.query,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)
        self.assertDictEqual(
            result,
            {
                "data": {
                    "userDetails": {
                        "activeStatus": self.user.get_active_status_display().upper(),
                        "avatar": self.user.avatar,
                        "email": self.user.email,
                        "firstName": self.user.first_name,
                        "id": self.user.id,
                        "isStaff": self.user.is_staff,
                        "isSuperuser": self.user.is_superuser,
                        "lastName": self.user.last_name,
                        "phone": self.user.phone,
                    }
                }
            },
        )

    def test_users_detail_unauthenticated(self):
        result = self.client.execute(
            self.query,
            context=self.non_auth_request,
        )
        self._check_none_auth_error(result)
