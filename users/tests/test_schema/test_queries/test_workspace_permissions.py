from unittest.mock import MagicMock

from django.test import override_settings

from common.tests.factories import RoleFactory, UserFactory
from common.tests.schema.base_test import BaseTestCase


class WorkspacePermissionsQueryTestCase(BaseTestCase):
    maxDiff = None

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # Add VIEW_WORKSPACE permission to the role so tests can access workspace permissions query
        cls.role.permissions.add(cls.perms["view_workspace"])

    def setUp(self):
        super().setUp()
        self.query = """
        query WorkspacePermissions($orgId: Int!) {
            workspacePermissions(orgId: $orgId) {
                id
                title
                codename
            }
        }
        """

    def test_workspace_permissions_query_success(self):
        """Test successful workspace permissions query"""
        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)
        self.assertIn("data", result)
        self.assertIn("workspacePermissions", result["data"])
        self.assertIsInstance(result["data"]["workspacePermissions"], list)

    def test_workspace_permissions_query_unauthenticated(self):
        """Test workspace permissions query with unauthenticated user"""
        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.non_auth_request,
        )
        # Check that there are errors and it's an authentication error
        self.assertIn("errors", result)
        self._check_none_auth_error(result)

    def test_workspace_permissions_query_without_permission(self):
        """Test workspace permissions query without VIEW_WORKSPACE permission"""

        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request_no_perm,
        )
        self.assertIn("errors", result)
        self.assertEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "status": 403,
                    "status_text": "Forbidden",
                    "reason": {"user": "Permission denied"},
                }
            },
        )

    @override_settings(
        WORKSPACE_ROLES_CODENAMES=[
            "workspace-add",
            "workspace-change",
            "workspace-delete",
            "workspace-view",
        ]
    )
    def test_workspace_permissions_query_returns_workspace_roles(self):
        """Test that workspace permissions query returns workspace-specific roles"""
        # Create workspace roles that match the settings
        workspace_roles = []
        for codename in [
            "workspace-add",
            "workspace-change",
            "workspace-delete",
            "workspace-view",
        ]:
            role = RoleFactory.create(
                codename=codename, title=f"Workspace {codename.split('-')[1].title()}"
            )
            workspace_roles.append(role)

        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)

        # Check that workspace roles are returned
        returned_codenames = [
            role["codename"] for role in result["data"]["workspacePermissions"]
        ]
        expected_codenames = [
            "workspace-add",
            "workspace-change",
            "workspace-delete",
            "workspace-view",
        ]

        for expected_codename in expected_codenames:
            self.assertIn(expected_codename, returned_codenames)

    def test_workspace_permissions_query_structure(self):
        """Test that workspace permissions query returns correct structure"""
        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)

        # Check structure of each workspace permission
        for workspace_permission in result["data"]["workspacePermissions"]:
            self.assertIn("id", workspace_permission)
            self.assertIn("title", workspace_permission)
            self.assertIn("codename", workspace_permission)

            # Verify data types
            self.assertIsInstance(
                workspace_permission["id"], str
            )  # GraphQL returns string IDs
            self.assertIsInstance(workspace_permission["title"], str)
            self.assertIsInstance(workspace_permission["codename"], str)

            # Verify non-empty values
            self.assertGreater(len(workspace_permission["title"]), 0)
            self.assertGreater(len(workspace_permission["codename"]), 0)

    def test_workspace_permissions_query_invalid_organization(self):
        """Test workspace permissions query with invalid organization ID"""
        variables = {
            "orgId": 99999,  # Non-existent organization
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertIn("errors", result)
        self.assertEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "status": 404,
                    "status_text": "Not Found",
                    "reason": {"orgId": "Organization with id 99999 not found"},
                }
            },
        )

    @override_settings(WORKSPACE_ROLES_CODENAMES=[])
    def test_workspace_permissions_query_empty_settings(self):
        """Test workspace permissions query when WORKSPACE_ROLES_CODENAMES is empty"""
        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)

        # Should return empty list when no workspace role codenames are configured
        self.assertEqual(len(result["data"]["workspacePermissions"]), 0)

    def test_workspace_permissions_query_with_user_having_workspace_permission(self):
        """Test workspace permissions query with user having VIEW_WORKSPACE permission"""
        # Create user with VIEW_WORKSPACE permission
        role_with_workspace_perm = RoleFactory.create(
            organization=self.organization, permissions=[self.perms["view_workspace"]]
        )
        user_with_workspace_perm = UserFactory.create(
            organization=self.organization, roles=[role_with_workspace_perm]
        )

        auth_request_workspace_perm = MagicMock()
        auth_request_workspace_perm.user = user_with_workspace_perm

        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=auth_request_workspace_perm,
        )
        self.assertNotIn("errors", result)
        self.assertIsInstance(result["data"]["workspacePermissions"], list)
