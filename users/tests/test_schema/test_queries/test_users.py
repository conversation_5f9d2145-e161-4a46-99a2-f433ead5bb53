from unittest.mock import MagicMock, patch

from app.settings_base import MAIN_OWNER_ROLE_CODENAME
from common.tests.factories.role import RoleFactory
from common.tests.factories.user import UserFactory
from common.tests.factories.workspace import WorkspaceFactory
from common.tests.schema.query import BaseQueryTestCase
from users.models import ActiveStatusChoices


class UsersQueryTestCase(BaseQueryTestCase):
    maxDiff = None

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # Add VIEW_USER and VIEW_WORKSPACE permissions to the role so tests can access users query
        cls.role.permissions.add(cls.perms["view_user"])
        cls.role.permissions.add(cls.perms["view_workspace"])

        # Create an owner user for the organization (required by users query)
        cls.owner_role = RoleFactory.create(
            codename=MAIN_OWNER_ROLE_CODENAME, title="Main Owner"
        )
        cls.owner_user = UserFactory.create(
            organization=cls.organization, roles=[cls.owner_role]
        )

    def setUp(self):
        super().setUp()
        self.query = """
        query Users($orgId: Int!, $pk: Int, $workspaceId: Int, $excludeIndividuals: Boolean, $pageInfo: PageInfo, $filters: [DjangoFilterInput]) {
            users(orgId: $orgId, pk: $pk, workspaceId: $workspaceId, excludeIndividuals: $excludeIndividuals, pageInfo: $pageInfo, filters: $filters) {
                count
                data {
                    id
                    firstName
                    lastName
                    email
                    phone
                    isSuperuser
                    isStaff
                    activeStatus
                    avatar
                    role {
                        title
                        codename
                        usersCount
                        permissions {
                            model
                            groupedPermissions {
                                title
                                permissions {
                                    id
                                    codename
                                    name
                                }
                            }
                       }
                    }
                }
                count
            }
        }
        """

    def test_users_query_success(self):
        """Test successful users query with basic parameters"""
        variables = {"orgId": self.organization.id}
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)
        self.assertIn("data", result)
        self.assertEqual(result["data"]["users"]["count"], 1)
        self.assertIn("role", result["data"]["users"]["data"][0])
        result["data"]["users"]["data"][0].pop("role")
        self.assertDictEqual(
            result["data"]["users"]["data"][0],
            {
                "activeStatus": self.user.get_active_status_display().upper(),
                "avatar": self.user.avatar,
                "email": self.user.email,
                "firstName": self.user.first_name,
                "id": self.user.id,
                "isStaff": self.user.is_staff,
                "isSuperuser": self.user.is_superuser,
                "lastName": self.user.last_name,
                "phone": self.user.phone,
            },
        )

    def test_users_query_unauthenticated(self):
        """Test users query with unauthenticated user"""
        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.non_auth_request,
        )
        # Check that there are errors and it's an authentication error
        self.assertIn("errors", result)
        self._check_none_auth_error(result)

    def test_users_query_without_permission(self):
        """Test users query without VIEW_USER permission"""
        # Create a user without VIEW_USER permission
        role_without_permission = RoleFactory.create(
            organization=self.organization, permissions=[]
        )
        user_without_permission = UserFactory.create(
            organization=self.organization, roles=[role_without_permission]
        )

        auth_request_no_perm = MagicMock()
        auth_request_no_perm.user = user_without_permission

        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=auth_request_no_perm,
        )
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "status": 403,
                    "status_text": "Forbidden",
                    "reason": {"user": "Permission denied"},
                }
            },
        )

    def test_users_query_with_pk_filter(self):
        """Test users query with specific user pk"""
        # Create additional user
        additional_user = UserFactory.create(
            organization=self.organization, roles=[self.role]
        )

        variables = {
            "orgId": self.organization.id,
            "pk": additional_user.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)
        self.assertEqual(len(result["data"]["users"]["data"]), 1)
        self.assertEqual(result["data"]["users"]["data"][0]["id"], additional_user.id)

    def test_users_query_with_workspace_filter(self):
        """Test users query filtered by workspace"""
        # Create workspace and add users to it
        workspace = WorkspaceFactory.create(
            organization=self.organization, owner=self.user
        )
        workspace_user = UserFactory.create(
            organization=self.organization, roles=[self.role]
        )
        # Create a workspace-specific role
        workspace_role = RoleFactory.create(
            codename="workspace-view",
            title="Workspace Viewer",
            permissions=[self.perms["view_workspace"]],
        )
        workspace.acl_add_user(workspace_user, roles=[workspace_role])

        variables = {
            "orgId": self.organization.id,
            "workspaceId": workspace.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)
        self.assertEquals(result["data"]["users"]["count"], 1)
        self.assertEquals(result["data"]["users"]["data"][0]["id"], workspace_user.id)

    def test_users_query_exclude_individuals_from_workspace(self):
        """Test users query excluding individuals from workspace"""
        # Create workspace and add users to it
        workspace = WorkspaceFactory.create(
            organization=self.organization, owner=self.user
        )
        workspace_user = UserFactory.create(
            organization=self.organization, roles=[self.role]
        )
        # Create a workspace-specific role
        workspace_role = RoleFactory.create(
            codename="workspace-view",
            title="Workspace Viewer",
            permissions=[self.perms["view_workspace"]],
        )
        workspace.acl_add_user(workspace_user, roles=[workspace_role])

        variables = {
            "orgId": self.organization.id,
            "workspaceId": workspace.id,
            "excludeIndividuals": True,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)
        # Should exclude workspace_user
        user_ids = [user["id"] for user in result["data"]["users"]["data"]]
        self.assertNotIn(workspace_user.id, user_ids)

    def test_users_query_with_pagination(self):
        """Test users query with pagination"""
        # Create multiple users
        for i in range(5):
            UserFactory.create(organization=self.organization, roles=[self.role])

        variables = {
            "orgId": self.organization.id,
            "pageInfo": {"limit": 2, "offset": 0},
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)
        self.assertLessEqual(len(result["data"]["users"]["data"]), 2)
        self.assertGreaterEqual(
            result["data"]["users"]["count"], 5
        )  # At least 5 users created + existing ones

    def test_users_query_with_filters(self):
        """Test users query with Django filters"""
        # Create user with specific email
        test_user = UserFactory.create(
            organization=self.organization,
            roles=[self.role],
            email="<EMAIL>",
        )

        variables = {
            "orgId": self.organization.id,
            "filters": [
                {
                    "field": "email",
                    "value": test_user.email,
                    "clause": "exact",
                }
            ],
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)
        self.assertEqual(len(result["data"]["users"]["data"]), 1)
        self.assertEqual(result["data"]["users"]["data"][0]["email"], test_user.email)

    def test_users_query_excludes_deleted_users(self):
        """Test that deleted users are excluded from results"""
        # Create deleted user
        deleted_user = UserFactory.create(
            organization=self.organization,
            roles=[self.role],
            active_status=ActiveStatusChoices.DELETED,
        )

        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)
        user_ids = [user["id"] for user in result["data"]["users"]["data"]]
        self.assertTrue(len(user_ids) > 0)
        self.assertNotIn(deleted_user.id, user_ids)

    def test_users_query_invalid_organization(self):
        """Test users query with invalid organization ID"""
        variables = {
            "orgId": 99999,  # Non-existent organization
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertIn("errors", result)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {"orgId": "Organization with id 99999 not found"},
                    "status": 404,
                    "status_text": "Not Found",
                }
            },
        )

    def test_users_query_invalid_workspace(self):
        """Test users query with invalid workspace ID"""
        variables = {
            "orgId": self.organization.id,
            "workspaceId": 99999,  # Non-existent workspace
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)

    @patch("users.schema.query.get_owner_user")
    def test_users_query_no_owner_user(self, mock_get_owner_user):
        """Test users query when no owner user is found"""
        mock_get_owner_user.return_value = None

        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertIn("errors", result)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {"user": "No owner user found"},
                    "status": 400,
                    "status_text": "Bad Request",
                }
            },
        )
