from rest_framework.exceptions import ErrorDetail

from common.tests.factories import OrganizationFactory, RoleFactory
from common.tests.schema.base_test import BaseTestCase


class UpdateRoleMutationTestCase(BaseTestCase):
    maxDiff = None

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # Add CHANGE_ROLE permission to the role so tests can access update role mutation
        cls.role.permissions.add(cls.perms["change_role"])

    def setUp(self):
        super().setUp()
        self.mutation = """
        mutation UpdateRole($dataInput: UpdateRoleInputType!) {
            updateRole(dataInput: $dataInput) {
                role {
                    id
                    title
                    codename
                    permissions {
                        id
                        name
                        codename
                    }
                }
            }
        }
        """

        # Create a test role to update
        self.test_role = RoleFactory.create(
            title="Original Role Title", permissions=[self.perms["view_user"]]
        )
        self.organization.roles.add(self.test_role)

    def test_update_role_title_success(self):
        """Test successful role title update"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "roleId": self.test_role.id,
                "title": "Updated Role Title",
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        # Verify role data
        role_data = result["data"]["updateRole"]["role"]
        self.assertEqual(role_data["title"], "Updated Role Title")
        self.assertEqual(int(role_data["id"]), self.test_role.id)
        # Verify permissions remain unchanged
        self.assertEqual(len(role_data["permissions"]), 1)
        self.assertEqual(
            int(role_data["permissions"][0]["id"]), self.perms["view_user"].id
        )

        # Verify role was updated in database
        self.test_role.refresh_from_db()
        self.assertEqual(self.test_role.title, "Updated Role Title")

    def test_update_role_permissions_success(self):
        """Test successful role permissions update"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "roleId": self.test_role.id,
                "permissionsList": [
                    self.perms["view_user"].id,
                    self.perms["add_user"].id,
                    self.perms["change_user"].id,
                ],
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        # Verify role permissions
        role_data = result["data"]["updateRole"]["role"]
        self.assertEqual(len(role_data["permissions"]), 3)
        permission_ids = [int(perm["id"]) for perm in role_data["permissions"]]
        self.assertIn(self.perms["view_user"].id, permission_ids)
        self.assertIn(self.perms["add_user"].id, permission_ids)
        self.assertIn(self.perms["change_user"].id, permission_ids)

        # Verify permissions were updated in database
        self.test_role.refresh_from_db()
        self.assertEqual(self.test_role.permissions.count(), 3)

    def test_update_role_title_and_permissions_success(self):
        """Test successful role title and permissions update"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "roleId": self.test_role.id,
                "title": "Completely Updated Role",
                "permissionsList": [
                    self.perms["view_workspace"].id,
                    self.perms["add_workspace"].id,
                ],
            }
        }

        result = self._execute_mutation(self.mutation, variables)

        # Verify both title and permissions were updated
        role_data = result["data"]["updateRole"]["role"]
        self.assertEqual(role_data["title"], "Completely Updated Role")
        self.assertEqual(len(role_data["permissions"]), 2)

        permission_ids = [int(perm["id"]) for perm in role_data["permissions"]]
        self.assertIn(self.perms["view_workspace"].id, permission_ids)
        self.assertIn(self.perms["add_workspace"].id, permission_ids)

    def test_update_role_unauthenticated(self):
        """Test update role mutation with unauthenticated user"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "roleId": self.test_role.id,
                "title": "Updated Title",
            }
        }

        result = self._execute_mutation(
            self.mutation, variables, context=self.non_auth_request
        )
        self._check_none_auth_error(result)

    def test_update_role_without_permission(self):
        """Test update role mutation without CHANGE_ROLE permission"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "roleId": self.test_role.id,
                "title": "Updated Title",
            }
        }

        result = self._execute_mutation(
            self.mutation, variables, context=self.auth_request_no_perm
        )
        self._check_unauthorized_error(result)

    def test_update_role_invalid_organization(self):
        """Test update role mutation with invalid organization ID"""
        org_id = 99999
        variables = {
            "dataInput": {
                "orgId": org_id,  # Non-existent organization
                "roleId": self.test_role.id,
                "title": "Updated Title",
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "status": 404,
                    "status_text": "Not Found",
                    "reason": {"orgId": f"Organization with id {org_id} not found"},
                }
            },
        )

    def test_update_role_invalid_role_id(self):
        """Test update role mutation with invalid role ID"""
        role_id = 99999
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "roleId": role_id,  # Non-existent role
                "title": "Updated Title",
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {"roleId": f"Invalid role_id {role_id}"},
                    "status": 404,
                    "status_text": "Not Found",
                }
            },
        )

    def test_update_role_role_not_in_organization(self):
        """Test update role mutation with role not belonging to organization"""
        # Create role in different organization
        other_org = OrganizationFactory()
        other_role = RoleFactory.create(title="Other Org Role")
        other_org.roles.add(other_role)

        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "roleId": other_role.id,  # Role from different org
                "title": "Updated Title",
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {"roleId": f"Invalid role_id {other_role.id}"},
                    "status": 404,
                    "status_text": "Not Found",
                }
            },
        )

    def test_update_role_empty_title(self):
        """Test update role mutation with empty title"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "roleId": self.test_role.id,
                "title": "",  # Empty title
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {
                        "title": [
                            ErrorDetail(
                                string="This field may not be blank.", code="blank"
                            )
                        ]
                    },
                    "status": 400,
                    "status_text": "Bad Request",
                }
            },
        )

    def test_update_role_invalid_permissions(self):
        """Test update role mutation with invalid permission IDs"""
        perm_id = 99999
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "roleId": self.test_role.id,
                "permissionsList": [perm_id],  # Non-existent permissions
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {
                        "permissionsList": {
                            0: [
                                ErrorDetail(
                                    string=f'Invalid pk "{perm_id}" - object does not exist.',
                                    code="does_not_exist",
                                )
                            ]
                        }
                    },
                    "status": 400,
                    "status_text": "Bad Request",
                }
            },
        )

    def test_update_role_clear_permissions(self):
        """Test update role mutation to clear all permissions"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "roleId": self.test_role.id,
                "permissionsList": [],  # Empty permissions list
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {
                        "permissionsList": [
                            ErrorDetail(
                                string="This list may not be empty.", code="empty"
                            )
                        ]
                    },
                    "status": 400,
                    "status_text": "Bad Request",
                }
            },
        )
