from common.tests.factories import OrganizationFactory, RoleFactory, UserFactory
from common.tests.schema.base_test import BaseTestCase


class ChangeUserRoleMutationTestCase(BaseTestCase):
    maxDiff = None

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # Add CHANGE_ROLE permission to the role so tests can access change user role mutation
        cls.role.permissions.add(cls.perms["change_role"])

    def setUp(self):
        super().setUp()
        self.mutation = """
        mutation ChangeUserRole($dataInput: ChangeUserRoleInputType!) {
            changeUserRole(dataInput: $dataInput) {
                user {
                    id
                    firstName
                    lastName
                    email
                    role {
                        id
                        title
                        codename
                        permissions {
                            id
                            name
                            codename
                        }
                    }
                }
            }
        }
        """

        # Create a test user and roles
        self.old_role = RoleFactory.create(
            title="Old Role", permissions=[self.perms["view_user"]]
        )
        self.organization.roles.add(self.old_role)

        self.new_role = RoleFactory.create(
            title="New Role",
            permissions=[self.perms["view_user"], self.perms["add_user"]],
        )
        self.organization.roles.add(self.new_role)

        self.target_user = UserFactory.create(
            organization=self.organization,
            roles=[self.old_role],
            first_name="Target",
            last_name="User",
            email="<EMAIL>",
        )

    def test_change_user_role_success(self):
        """Test successful user role change"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "userId": self.target_user.id,
                "roleId": self.new_role.id,
            }
        }

        result = self._execute_mutation(self.mutation, variables)

        # Verify user data
        user_data = result["data"]["changeUserRole"]["user"]
        self.assertEqual(int(user_data["id"]), self.target_user.id)
        self.assertEqual(user_data["email"], "<EMAIL>")
        self.assertEqual(int(user_data["role"]["id"]), self.new_role.id)
        self.assertEqual(user_data["role"]["title"], "New Role")

        # Verify permissions are included
        self.assertEqual(len(user_data["role"]["permissions"]), 2)
        permission_ids = [int(perm["id"]) for perm in user_data["role"]["permissions"]]
        self.assertIn(self.perms["view_user"].id, permission_ids)
        self.assertIn(self.perms["add_user"].id, permission_ids)

        # Verify role was changed in database
        self.target_user.refresh_from_db()
        user_roles = list(self.organization.acl_user_roles(self.target_user))
        self.assertIn(self.new_role, user_roles)
        self.assertNotIn(self.old_role, user_roles)

    def test_change_user_role_unauthenticated(self):
        """Test change user role mutation with unauthenticated user"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "userId": self.target_user.id,
                "roleId": self.new_role.id,
            }
        }

        result = self._execute_mutation(
            self.mutation, variables, context=self.non_auth_request
        )
        self._check_none_auth_error(result)

    def test_change_user_role_without_permission(self):
        """Test change user role mutation without CHANGE_USER permission"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "userId": self.target_user.id,
                "roleId": self.new_role.id,
            }
        }

        result = self._execute_mutation(
            self.mutation, variables, context=self.auth_request_no_perm
        )
        self._check_unauthorized_error(result)

    def test_change_user_role_invalid_organization(self):
        """Test change user role mutation with invalid organization ID"""
        org_id = 99999
        variables = {
            "dataInput": {
                "orgId": org_id,  # Non-existent organization
                "userId": self.target_user.id,
                "roleId": self.new_role.id,
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "status": 404,
                    "status_text": "Not Found",
                    "reason": {"orgId": f"Organization with id {org_id} not found"},
                }
            },
        )

    def test_change_user_role_invalid_user_id(self):
        """Test change user role mutation with invalid user ID"""
        user_id = 99999
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "userId": user_id,  # Non-existent user
                "roleId": self.new_role.id,
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {
                        "userId": f"User {user_id} not in organization {self.organization.id}"
                    },
                    "status": 404,
                    "status_text": "Not Found",
                }
            },
        )

    def test_change_user_role_invalid_role_id(self):
        """Test change user role mutation with invalid role ID"""
        role_id = 99999
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "userId": self.target_user.id,
                "roleId": role_id,  # Non-existent role
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {"roleId": f"Invalid role_id {99999}."},
                    "status": 404,
                    "status_text": "Not Found",
                }
            },
        )

    def test_change_user_role_user_not_in_organization(self):
        """Test change user role mutation with user not in organization"""
        # Create user in different organization

        other_org = OrganizationFactory()
        other_user = UserFactory.create(organization=other_org)

        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "userId": other_user.id,  # User from different org
                "roleId": self.new_role.id,
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {
                        "userId": f"User {other_user.id} not in organization {self.organization.id}"
                    },
                    "status": 404,
                    "status_text": "Not Found",
                }
            },
        )

    def test_change_user_role_role_not_in_organization(self):
        """Test change user role mutation with role not in organization"""
        # Create role in different organization

        other_org = OrganizationFactory()
        other_role = RoleFactory.create(title="Other Org Role")
        other_org.roles.add(other_role)

        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "userId": self.target_user.id,
                "roleId": other_role.id,  # Role from different org
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {"roleId": f"Invalid role_id {other_role.id}."},
                    "status": 404,
                    "status_text": "Not Found",
                }
            },
        )

    def test_change_user_role_same_role(self):
        """Test change user role mutation when user already has the role"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "userId": self.target_user.id,
                "roleId": self.old_role.id,  # Same role user already has
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        user_data = result["data"]["changeUserRole"]["user"]
        self.assertEqual(int(user_data["role"]["id"]), self.old_role.id)

    def test_change_user_role_preserves_user_data(self):
        """Test that changing user role preserves other user data"""
        original_email = self.target_user.email
        original_first_name = self.target_user.first_name
        original_last_name = self.target_user.last_name

        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "userId": self.target_user.id,
                "roleId": self.new_role.id,
            }
        }

        result = self._execute_mutation(self.mutation, variables)

        # Verify user data is preserved
        user_data = result["data"]["changeUserRole"]["user"]
        self.assertEqual(user_data["email"], original_email)
        self.assertEqual(user_data["firstName"], original_first_name)
        self.assertEqual(user_data["lastName"], original_last_name)

        # Verify in database
        self.target_user.refresh_from_db()
        self.assertEqual(self.target_user.email, original_email)
        self.assertEqual(self.target_user.first_name, original_first_name)
        self.assertEqual(self.target_user.last_name, original_last_name)
