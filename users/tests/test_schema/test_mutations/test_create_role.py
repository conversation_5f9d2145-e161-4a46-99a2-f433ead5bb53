from gabbro.acl.models import Role
from rest_framework.exceptions import ErrorDetail

from common.tests.factories import OrganizationFactory, RoleFactory
from common.tests.schema.base_test import BaseTestCase


class CreateRoleMutationTestCase(BaseTestCase):
    maxDiff = None

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # Add ADD_ROLE permission to the role so tests can access create role mutation
        cls.role.permissions.add(cls.perms["add_role"])

    def setUp(self):
        super().setUp()
        self.mutation = """
        mutation CreateRole($dataInput: RoleInputType!) {
            createRole(dataInput: $dataInput) {
                role {
                    id
                    title
                    codename
                    permissions {
                        id
                        name
                        codename
                    }
                }
            }
        }
        """

    def test_create_role_success(self):
        """Test successful role creation"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "title": "Test Manager Role",
                "permissionsList": [
                    self.perms["view_user"].id,
                    self.perms["add_user"].id,
                ],
            }
        }

        result = self._execute_mutation(self.mutation, variables)

        # Verify role data
        role_data = result["data"]["createRole"]["role"]
        self.assertEqual(role_data["title"], "Test Manager Role")
        self.assertTrue(
            role_data["codename"].startswith(
                f"test-manager-role_code_{self.organization.id}_{self.user.id}_"
            )
        )
        self.assertEqual(len(role_data["permissions"]), 2)

        # Verify permissions are correctly assigned
        permission_ids = [int(perm["id"]) for perm in role_data["permissions"]]
        self.assertIn(self.perms["view_user"].id, permission_ids)
        self.assertIn(self.perms["add_user"].id, permission_ids)

        # Verify role was created in database and added to organization
        created_role = Role.objects.get(title="Test Manager Role")
        self.assertEqual(created_role.title, "Test Manager Role")
        self.assertIn(created_role, self.organization.roles.all())
        self.assertEqual(created_role.permissions.count(), 2)

    def test_create_role_unauthenticated(self):
        """Test create role mutation with unauthenticated user"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "title": "Test Role",
                "permissionsList": [self.perms["view_user"].id],
            }
        }

        result = self._execute_mutation(
            self.mutation, variables, context=self.non_auth_request
        )
        self._check_none_auth_error(result)

    def test_create_role_without_permission(self):
        """Test create role mutation without ADD_ROLE permission"""

        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "title": "Test Role",
                "permissionsList": [self.perms["view_user"].id],
            }
        }

        result = self._execute_mutation(
            self.mutation, variables, context=self.auth_request_no_perm
        )
        self._check_unauthorized_error(result)

    def test_create_role_invalid_organization(self):
        """Test create role mutation with invalid organization ID"""
        org_id = 99999
        variables = {
            "dataInput": {
                "orgId": org_id,  # Non-existent organization
                "title": "Test Role",
                "permissionsList": [self.perms["view_user"].id],
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "status": 404,
                    "status_text": "Not Found",
                    "reason": {"orgId": f"Organization with id {org_id} not found"},
                }
            },
        )

    def test_create_role_empty_title(self):
        """Test create role mutation with empty title"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "title": "",  # Empty title
                "permissionsList": [self.perms["view_user"].id],
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "status": 400,
                    "status_text": "Bad Request",
                    "reason": {
                        "title": [
                            ErrorDetail(
                                string="This field may not be blank.", code="blank"
                            )
                        ]
                    },
                }
            },
        )

    def test_create_role_invalid_permissions(self):
        """Test create role mutation with invalid permission IDs"""
        perm_id = 99999
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "title": "Test Role",
                "permissionsList": [perm_id],  # Non-existent permissions
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "status": 400,
                    "status_text": "Bad Request",
                    "reason": {
                        "permissionsList": {
                            0: [
                                ErrorDetail(
                                    string=f'Invalid pk "{perm_id}" - object does not exist.',
                                    code="does_not_exist",
                                )
                            ]
                        }
                    },
                }
            },
        )

    def test_create_role_empty_permissions(self):
        """Test create role mutation with empty permissions list"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "title": "Test Role",
                "permissionsList": [],  # Empty permissions list
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "status": 400,
                    "status_text": "Bad Request",
                    "reason": {
                        "permissionsList": [
                            ErrorDetail(
                                string="This list may not be empty.", code="empty"
                            )
                        ]
                    },
                }
            },
        )

    def test_create_role_duplicate_title_different_org(self):
        """Test create role mutation with duplicate title in different organization"""
        # Create another organization with a role with the same title

        other_org = OrganizationFactory()
        other_role = RoleFactory.create(title="Duplicate Title")
        other_org.roles.add(other_role)

        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "title": other_role.title,  # Same title as in other org
                "permissionsList": [self.perms["view_user"].id],
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        role_data = result["data"]["createRole"]["role"]
        self.assertEqual(role_data["title"], other_role.title)

    def test_create_role_codename_generation(self):
        """Test that role codename is generated correctly"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "title": "Special Characters & Spaces Role!",
                "permissionsList": [self.perms["view_user"].id],
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        role_data = result["data"]["createRole"]["role"]
        # Codename should be slugified and include org_id and user_id
        self.assertTrue(
            role_data["codename"].startswith("special-characters-spaces-role-_code_")
        )
        self.assertIn(str(self.organization.id), role_data["codename"])
        self.assertIn(str(self.user.id), role_data["codename"])
