from django.contrib.auth.models import Permission

from common.tests.factories import (
    UserFactory,
    RoleFactory,
    OrganizationFactory,
    WorkspaceFactory,
)
from common.tests.schema.base_test import BaseTestCase
from users.models import ActiveStatusChoices


class ChangeUserActiveStatusMutationTestCase(BaseTestCase):
    maxDiff = None

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # Add CHANGE_USER permission to the role so tests can access change user active status mutation
        cls.role.permissions.add(cls.perms["change_user"])

    def setUp(self):
        super().setUp()
        self.mutation = """
        mutation ChangeUserActiveStatus($dataInput: ChangeUserActiveStatusInputType!) {
            changeUserActiveStatus(dataInput: $dataInput) {
                user {
                    id
                    firstName
                    lastName
                    email
                    activeStatus
                    role {
                        id
                        title
                        codename
                    }
                }
            }
        }
        """

        # Create a test user

        self.test_role = RoleFactory.create(
            title="Test User Role",
            permissions=[
                Permission.objects.get(
                    codename="view_workspace", content_type__model="workspace"
                )
            ],
            codename="test-user-role-workspaces",
        )
        self.target_user = UserFactory.create(
            organization=self.organization,
            first_name="Target",
            roles=[self.role],
            last_name="User",
            email="<EMAIL>",
            active_status=ActiveStatusChoices.ACTIVE,
        )

    def test_change_user_active_status_to_inactive_success(self):
        """Test successful user active status change to inactive"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "userId": self.target_user.id,
                "activeStatus": ActiveStatusChoices.INACTIVE.value.upper(),
            }
        }

        result = self._execute_mutation(self.mutation, variables)

        # Verify user data
        user_data = result["data"]["changeUserActiveStatus"]["user"]
        self.assertEqual(int(user_data["id"]), self.target_user.id)
        self.assertEqual(user_data["email"], self.target_user.email)
        self.assertEqual(
            user_data["activeStatus"], ActiveStatusChoices.INACTIVE.value.upper()
        )

        # Verify status was changed in database
        self.target_user.refresh_from_db()
        self.assertEqual(self.target_user.active_status, ActiveStatusChoices.INACTIVE)

    def test_change_user_active_status_to_active_success(self):
        """Test successful user active status change to active"""
        # Start with inactive user
        self.target_user.active_status = ActiveStatusChoices.INACTIVE
        self.target_user.save()

        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "userId": self.target_user.id,
                "activeStatus": ActiveStatusChoices.ACTIVE.value.upper(),
            }
        }

        result = self._execute_mutation(self.mutation, variables)

        # Verify user data
        user_data = result["data"]["changeUserActiveStatus"]["user"]
        self.assertEqual(
            user_data["activeStatus"], ActiveStatusChoices.ACTIVE.value.upper()
        )

        # Verify status was changed in database
        self.target_user.refresh_from_db()
        self.assertEqual(self.target_user.active_status, ActiveStatusChoices.ACTIVE)

    def test_change_user_active_status_to_deleted_success(self):
        """Test successful user active status change to deleted"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "userId": self.target_user.id,
                "activeStatus": ActiveStatusChoices.DELETED.value.upper(),
            }
        }

        result = self._execute_mutation(self.mutation, variables)

        # Verify user data
        user_data = result["data"]["changeUserActiveStatus"]["user"]
        self.assertEqual(
            user_data["activeStatus"], ActiveStatusChoices.DELETED.value.upper()
        )

        # Verify status was changed in database
        self.target_user.refresh_from_db()
        self.assertEqual(self.target_user.active_status, ActiveStatusChoices.DELETED)

    def test_change_user_active_status_unauthenticated(self):
        """Test change user active status mutation with unauthenticated user"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "userId": self.target_user.id,
                "activeStatus": ActiveStatusChoices.INACTIVE.value.upper(),
            }
        }

        result = self._execute_mutation(
            self.mutation, variables, context=self.non_auth_request
        )
        self._check_none_auth_error(result)

    def test_change_user_active_status_without_permission(self):
        """Test change user active status mutation without CHANGE_USER permission"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "userId": self.target_user.id,
                "activeStatus": ActiveStatusChoices.INACTIVE.value.upper(),
            }
        }

        result = self._execute_mutation(
            self.mutation, variables, context=self.auth_request_no_perm
        )
        self._check_unauthorized_error(result)

    def test_change_user_active_status_invalid_organization(self):
        """Test change user active status mutation with invalid organization ID"""
        org_id = 99999
        variables = {
            "dataInput": {
                "orgId": org_id,  # Non-existent organization
                "userId": self.target_user.id,
                "activeStatus": ActiveStatusChoices.INACTIVE.value.upper(),
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "status": 404,
                    "status_text": "Not Found",
                    "reason": {"orgId": f"Organization with id {org_id} not found"},
                }
            },
        )

    def test_change_user_active_status_invalid_user_id(self):
        """Test change user active status mutation with invalid user ID"""
        user_id = 99999
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "userId": user_id,  # Non-existent user
                "activeStatus": ActiveStatusChoices.INACTIVE.value.upper(),
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {"userId": "Invalid user_id"},
                    "status": 400,
                    "status_text": "Bad Request",
                }
            },
        )

    def test_change_user_active_status_user_not_in_organization(self):
        """Test change user active status mutation with user not in organization"""
        # Create user in different organization

        other_org = OrganizationFactory()
        other_user = UserFactory.create(organization=other_org)
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "userId": other_user.id,  # User from different org
                "activeStatus": ActiveStatusChoices.INACTIVE.value.upper(),
            }
        }
        result = self._execute_mutation(self.mutation, variables)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {"userId": "Invalid user_id"},
                    "status": 400,
                    "status_text": "Bad Request",
                }
            },
        )

    def test_change_user_active_status_same_status(self):
        """Test change user active status mutation when user already has the status"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "userId": self.target_user.id,
                "activeStatus": ActiveStatusChoices.ACTIVE.value.upper(),  # Same status user already has
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        # Should still succeed
        user_data = result["data"]["changeUserActiveStatus"]["user"]
        self.assertEqual(
            user_data["activeStatus"], ActiveStatusChoices.ACTIVE.value.upper()
        )

    def test_change_user_active_status_preserves_user_data(self):
        """Test that changing user active status preserves other user data"""
        original_email = self.target_user.email
        original_first_name = self.target_user.first_name
        original_last_name = self.target_user.last_name

        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "userId": self.target_user.id,
                "activeStatus": ActiveStatusChoices.INACTIVE.value.upper(),
            }
        }
        result = self._execute_mutation(self.mutation, variables)
        # Verify user data is preserved
        user_data = result["data"]["changeUserActiveStatus"]["user"]
        self.assertEqual(user_data["email"], original_email)
        self.assertEqual(user_data["firstName"], original_first_name)
        self.assertEqual(user_data["lastName"], original_last_name)
        # Verify in database
        self.target_user.refresh_from_db()
        self.assertEqual(self.target_user.email, original_email)
        self.assertEqual(self.target_user.first_name, original_first_name)
        self.assertEqual(self.target_user.last_name, original_last_name)

    def test_change_user_active_status_delete_user_check_acl(self):
        """Test that changing user active status to deleted removes user from organization ACL"""
        workspace = WorkspaceFactory.create(
            organization=self.organization, owner=self.user
        )
        workspace.acl_add_user(self.target_user, roles=[self.test_role])
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "userId": self.target_user.id,
                "activeStatus": ActiveStatusChoices.DELETED.value.upper(),
            }
        }
        result = self._execute_mutation(self.mutation, variables)
        # Verify user is removed from organization ACL
        self.assertNotIn(self.target_user, self.organization.acl_individuals)
        # Verify user is removed from workspace ACL
        self.assertNotIn(self.target_user, workspace.acl_individuals)
