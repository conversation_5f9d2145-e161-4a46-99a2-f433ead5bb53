from django.contrib.auth.models import Permission
from gabbro.acl.models import Role

from common.tests.factories import (
    OrganizationFactory,
    RoleFactory,
    UserFactory,
    WorkspaceFactory,
)
from common.tests.schema.base_test import BaseTestCase
from users.models import ActiveStatusChoices


class DeleteRoleMutationTestCase(BaseTestCase):
    maxDiff = None

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # Add DELETE_ROLE permission to the role so tests can access delete role mutation
        cls.role.permissions.add(cls.perms["delete_role"])

    def setUp(self):
        super().setUp()
        self.mutation = """
        mutation DeleteRole($dataInput: DeleteRoleInputType!) {
            deleteRole(dataInput: $dataInput) {
                success
            }
        }
        """

        # Create a test role to delete
        self.test_role = RoleFactory.create(
            title="Role to Delete", permissions=[self.perms["view_user"]]
        )
        self.organization.roles.add(self.test_role)

    def test_delete_role_success(self):
        """Test successful role deletion"""
        role_id = self.test_role.id
        variables = {
            "dataInput": {"orgId": self.organization.id, "roleId": self.test_role.id}
        }
        result = self._execute_mutation(self.mutation, variables)
        # Verify success response
        self.assertTrue(result["data"]["deleteRole"]["success"])
        # Verify role was deleted from database
        self.assertFalse(Role.objects.filter(id=role_id).exists())

    def test_delete_role_with_users_success(self):
        """Test successful role deletion when role has users assigned"""
        # Create users with the role to be deleted
        user1 = UserFactory.create(
            organization=self.organization,
            roles=[self.test_role],
            active_status=ActiveStatusChoices.ACTIVE,
        )
        user2 = UserFactory.create(
            organization=self.organization,
            roles=[self.test_role],
            active_status=ActiveStatusChoices.ACTIVE,
        )

        # Create workspace and add users to it
        workspace = WorkspaceFactory.create(
            organization=self.organization, owner=self.user
        )
        workspace_role = RoleFactory.create(
            codename="workspace-view",
            title="Workspace Viewer",
            permissions=[
                Permission.objects.get(
                    codename="view_workspace", content_type__model="workspace"
                )
            ],
        )
        workspace.acl_add_user(user1, roles=[workspace_role])
        workspace.acl_add_user(user2, roles=[workspace_role])
        role_id = self.test_role.id
        variables = {
            "dataInput": {"orgId": self.organization.id, "roleId": self.test_role.id}
        }

        result = self._execute_mutation(self.mutation, variables)
        # Verify success response
        self.assertTrue(result["data"]["deleteRole"]["success"])
        # Verify role was deleted from database
        self.assertFalse(Role.objects.filter(id=role_id).exists())
        # Verify users were marked as deleted and removed from organization
        user1.refresh_from_db()
        user2.refresh_from_db()
        self.assertEqual(user1.active_status, ActiveStatusChoices.DELETED)
        self.assertEqual(user2.active_status, ActiveStatusChoices.DELETED)

        # Verify users were removed from workspaces
        workspace.refresh_from_db()
        workspace_users = workspace.acl_individuals
        self.assertNotIn(user1, workspace_users)
        self.assertNotIn(user2, workspace_users)

    def test_delete_role_unauthenticated(self):
        """Test delete role mutation with unauthenticated user"""
        variables = {
            "dataInput": {"orgId": self.organization.id, "roleId": self.test_role.id}
        }

        result = self._execute_mutation(
            self.mutation, variables, context=self.non_auth_request
        )
        self._check_none_auth_error(result)

    def test_delete_role_without_permission(self):
        """Test delete role mutation without DELETE_ROLE permission"""
        variables = {
            "dataInput": {"orgId": self.organization.id, "roleId": self.test_role.id}
        }
        result = self._execute_mutation(
            self.mutation, variables, context=self.auth_request_no_perm
        )
        self._check_unauthorized_error(result)

    def test_delete_role_invalid_organization(self):
        """Test delete role mutation with invalid organization ID"""
        org_id = 99999
        variables = {
            "dataInput": {
                "orgId": org_id,  # Non-existent organization
                "roleId": self.test_role.id,
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "status": 404,
                    "status_text": "Not Found",
                    "reason": {"orgId": f"Organization with id {org_id} not found"},
                }
            },
        )

    def test_delete_role_invalid_role_id(self):
        """Test delete role mutation with invalid role ID"""
        role_id = 99999
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "roleId": role_id,  # Non-existent role
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {"roleId": f"Invalid role_id {role_id}"},
                    "status": 404,
                    "status_text": "Not Found",
                }
            },
        )

    def test_delete_role_role_not_in_organization(self):
        """Test delete role mutation with role not belonging to organization"""
        # Create role in different organization

        other_org = OrganizationFactory()
        other_role = RoleFactory.create(title="Other Org Role")
        other_org.roles.add(other_role)

        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "roleId": other_role.id,  # Role from different org
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {"roleId": f"Invalid role_id {other_role.id}"},
                    "status": 404,
                    "status_text": "Not Found",
                }
            },
        )

    def test_delete_role_preserves_other_roles(self):
        """Test that deleting a role doesn't affect other roles"""
        # Create another role that should not be affected
        other_role = RoleFactory.create(
            title="Other Role", permissions=[self.perms["view_workspace"]]
        )
        self.organization.roles.add(other_role)

        other_role_id = other_role.id
        role_to_delete_id = self.test_role.id

        variables = {
            "dataInput": {"orgId": self.organization.id, "roleId": self.test_role.id}
        }

        self._execute_mutation(self.mutation, variables)
        # Verify target role was deleted
        self.assertFalse(Role.objects.filter(id=role_to_delete_id).exists())
        # Verify other role still exists
        self.assertTrue(Role.objects.filter(id=other_role_id).exists())
        other_role.refresh_from_db()
        self.assertEqual(other_role.title, "Other Role")
