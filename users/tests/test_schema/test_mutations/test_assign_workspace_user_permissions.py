from common.tests.factories import (
    UserFactory,
    RoleFactory,
    WorkspaceFactory,
    OrganizationFactory,
)
from common.tests.schema.base_test import BaseTestCase


class AssignWorkspaceUserPermissionsMutationTestCase(BaseTestCase):
    maxDiff = None

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # Add CHANGE_ROLE permission to the role so tests can access assign workspace user permissions mutation
        print(cls.user.has_perm("change_user"))
        cls.role.permissions.add(cls.perms["change_role"])
        print(cls.user.has_perm("change_user"))

    def setUp(self):
        super().setUp()
        self.mutation = """
        mutation AssignWorkspaceUserPermissions($dataInput: AssignWorkspaceUserPermissionsInputType!) {
            assignWorkspaceUserPermissions(dataInput: $dataInput) {
                users {
                  id
                  role {
                    codename
                    permissions {
                      codename
                    }
                    title
                  }
                  workspacePermissions {
                    codename
                    title
                  }
                }
              }
        }
        """

        # Create test workspace and users
        self.workspace = WorkspaceFactory.create(
            organization=self.organization, owner=self.user
        )

        self.target_user = UserFactory.create(
            organization=self.organization,
            roles=[self.role],
            first_name="Target",
            last_name="User",
            email="<EMAIL>",
        )

        # Create workspace roles
        self.workspace_view_role = RoleFactory.create(
            codename="workspace-view", title="Workspace Viewer"
        )
        self.workspace_view_role.permissions.add(self.perms["view_workspace"])
        self.workspace_edit_role = RoleFactory.create(
            codename="workspace-change", title="Workspace Editor"
        )
        self.workspace_edit_role.permissions.add(self.perms["change_workspace"])

    def test_assign_workspace_user_permissions_success(self):
        """Test successful workspace user permissions assignment"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "workspaceId": self.workspace.id,
                "usersIds": [self.target_user.id],
                "permissionsIds": [
                    self.workspace_view_role.id,
                    self.workspace_edit_role.id,
                ],
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertDictEqual(
            result["data"],
            {
                "assignWorkspaceUserPermissions": {
                    "users": [
                        {
                            "id": self.target_user.id,
                            "role": {
                                "codename": self.role.codename,
                                "permissions": [
                                    {"codename": self.perms["change_role"].codename},
                                    {
                                        "codename": self.perms[
                                            "view_organization"
                                        ].codename
                                    },
                                ],
                                "title": self.role.title,
                            },
                            "workspacePermissions": [
                                {
                                    "codename": self.workspace_edit_role.codename,
                                    "title": self.workspace_edit_role.title,
                                },
                                {
                                    "codename": self.workspace_view_role.codename,
                                    "title": self.workspace_view_role.title,
                                },
                            ],
                        }
                    ]
                },
            },
        )
        # Verify user was added to workspace with correct roles
        workspace_users = self.workspace.acl_individuals
        self.assertIn(self.target_user, workspace_users)

        # Verify user has the assigned roles in the workspace
        user_roles = self.workspace.acl_user_roles(self.target_user)
        self.assertIn(self.workspace_view_role, user_roles)
        self.assertIn(self.workspace_edit_role, user_roles)

    def test_assign_workspace_user_permissions_single_role_success(self):
        """Test successful workspace user permissions assignment with single role"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "workspaceId": self.workspace.id,
                "usersIds": [self.target_user.id],
                "permissionsIds": [self.workspace_view_role.id],
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertNotIn("errors", result)
        # Verify user was added to workspace with correct role
        workspace_users = self.workspace.acl_individuals
        self.assertIn(self.target_user, workspace_users)

        user_roles = self.workspace.acl_user_roles(self.target_user)
        self.assertIn(self.workspace_view_role, user_roles)
        self.assertNotIn(self.workspace_edit_role, user_roles)

    def test_assign_workspace_user_permissions_update_existing_success(self):
        """Test successful workspace user permissions update for existing user"""
        # First add user with one role
        self.workspace.acl_add_user(self.target_user, roles=[self.workspace_view_role])

        # Now update with different roles
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "workspaceId": self.workspace.id,
                "usersIds": self.target_user.id,
                "permissionsIds": [self.workspace_edit_role.id],
            }
        }

        result = self._execute_mutation(self.mutation, variables)

        # Verify user roles were updated
        user_roles = self.workspace.acl_user_roles(self.target_user)
        self.assertIn(self.workspace_edit_role, user_roles)
        # Previous role should be replaced
        self.assertNotIn(self.workspace_view_role, user_roles)

    def test_assign_workspace_user_permissions_unauthenticated(self):
        """Test assign workspace user permissions mutation with unauthenticated user"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "workspaceId": self.workspace.id,
                "usersIds": self.target_user.id,
                "permissionsIds": [self.workspace_view_role.id],
            }
        }

        result = self._execute_mutation(
            self.mutation, variables, context=self.non_auth_request
        )
        self._check_none_auth_error(result)

    def test_assign_workspace_user_permissions_without_permission(self):
        """Test assign workspace user permissions mutation without CHANGE_WORKSPACE permission"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "workspaceId": self.workspace.id,
                "usersIds": self.target_user.id,
                "permissionsIds": [self.workspace_view_role.id],
            }
        }

        result = self._execute_mutation(
            self.mutation, variables, context=self.auth_request_no_perm
        )
        self._check_unauthorized_error(result)

    def test_assign_workspace_user_permissions_invalid_organization(self):
        """Test assign workspace user permissions mutation with invalid organization ID"""
        org_id = 99999
        variables = {
            "dataInput": {
                "orgId": org_id,  # Non-existent organization
                "workspaceId": self.workspace.id,
                "usersIds": self.target_user.id,
                "permissionsIds": [self.workspace_view_role.id],
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertIn("errors", result)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "status": 404,
                    "status_text": "Not Found",
                    "reason": {"orgId": f"Organization with id {org_id} not found"},
                }
            },
        )

    def test_assign_workspace_user_permissions_invalid_workspace_id(self):
        """Test assign workspace user permissions mutation with invalid workspace ID"""
        workspace_id = 99999
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "workspaceId": workspace_id,  # Non-existent workspace
                "usersIds": [self.target_user.id],
                "permissionsIds": [self.workspace_view_role.id],
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertIn("errors", result)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "status": 404,
                    "status_text": "Not Found",
                    "reason": {"workspaceId": f"Invalid workspace {workspace_id}"},
                }
            },
        )

    def test_assign_workspace_user_permissions_invalid_user_id(self):
        """Test assign workspace user permissions mutation with invalid user ID"""
        user_id = 99999
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "workspaceId": self.workspace.id,
                "usersIds": [user_id],  # Non-existent user
                "permissionsIds": [self.workspace_view_role.id],
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertIn("errors", result)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "status": 404,
                    "status_text": "Not Found",
                    "reason": {"userId": f"Invalid users [{user_id}]"},
                }
            },
        )

    def test_assign_workspace_user_permissions_invalid_role_ids(self):
        """Test assign workspace user permissions mutation with invalid role IDs"""
        role_id = 99999
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "workspaceId": self.workspace.id,
                "usersIds": [self.target_user.id],
                "permissionsIds": [role_id],  # Non-existent roles
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertIn("errors", result)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "status": 404,
                    "status_text": "Not Found",
                    "reason": {"permissionsIds": f"Invalid permissions [{role_id}]"},
                }
            },
        )

    def test_assign_workspace_user_permissions_user_not_in_organization(self):
        """Test assign workspace user permissions mutation with user not in organization"""
        # Create user in different organization
        other_org = OrganizationFactory()
        other_user = UserFactory.create(organization=other_org)
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "workspaceId": self.workspace.id,
                "usersIds": [other_user.id],  # User from different org
                "permissionsIds": [self.workspace_view_role.id],
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "status": 404,
                    "status_text": "Not Found",
                    "reason": {"userId": f"Invalid users [{other_user.id}]"},
                }
            },
        )

    def test_assign_workspace_user_permissions_workspace_not_in_organization(self):
        """Test assign workspace user permissions mutation with workspace not in organization"""
        # Create workspace in different organization

        other_org = OrganizationFactory()
        other_workspace = WorkspaceFactory.create(
            organization=other_org, owner=self.user
        )

        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "workspaceId": other_workspace.id,  # Workspace from different org
                "usersIds": self.target_user.id,
                "permissionsIds": [self.workspace_view_role.id],
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {
                        "workspaceId": f"Invalid workspace {other_workspace.id}"
                    },
                    "status": 404,
                    "status_text": "Not Found",
                },
            },
        )

    def test_assign_workspace_user_permissions_empty_roles_list(self):
        """Test assign workspace user permissions mutation with empty roles list"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "workspaceId": self.workspace.id,
                "usersIds": [self.target_user.id],
                "permissionsIds": [],  # Empty roles list
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertNotIn("errors", result)
        # Verify user was removed from workspace
        workspace_users = self.workspace.acl_individuals
        self.assertNotIn(self.target_user, workspace_users)

    def test_assign_workspace_user_permissions_remove_user_from_workspace(self):
        """Test removing user from workspace by assigning empty roles"""
        # First add user to workspace
        self.workspace.acl_add_user(self.target_user, roles=[self.workspace_view_role])
        # Verify user is in workspace
        workspace_users = self.workspace.acl_individuals
        self.assertIn(self.target_user, workspace_users)
        # Now remove user by assigning empty roles
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "workspaceId": self.workspace.id,
                "usersIds": [self.target_user.id],
                "permissionsIds": [],
            }
        }
        result = self._execute_mutation(self.mutation, variables)
        self.assertNotIn("errors", result)
        # Verify user was removed from workspace
        workspace_users = self.workspace.acl_individuals
        self.assertNotIn(self.target_user, workspace_users)

    def test_assign_workspace_user_permissions_preserves_other_users(self):
        """Test that assigning permissions to one user doesn't affect other users"""
        # Create another user and add to workspace
        other_user = UserFactory.create(
            organization=self.organization, roles=[self.role]
        )
        self.workspace.acl_add_user(other_user, roles=[self.workspace_edit_role])
        # Assign permissions to target user
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "workspaceId": self.workspace.id,
                "usersIds": [self.target_user.id],
                "permissionsIds": [self.workspace_view_role.id],
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertNotIn("errors", result)
        # Verify target user has correct permissions
        target_user_roles = self.workspace.acl_user_roles(self.target_user)
        self.assertIn(self.workspace_view_role, target_user_roles)

        # Verify other user's permissions are unchanged
        other_user_roles = self.workspace.acl_user_roles(other_user)
        self.assertIn(self.workspace_edit_role, other_user_roles)
        self.assertNotIn(self.workspace_view_role, other_user_roles)
