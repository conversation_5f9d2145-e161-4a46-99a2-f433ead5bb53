from unittest.mock import patch

from django.core.exceptions import ValidationError

from common.tests.factories import RoleFactory
from common.tests.schema.base_test import BaseTestCase
from users.models import User, ActiveStatusChoices


class AddUserMutationTestCase(BaseTestCase):
    maxDiff = None

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # Add ADD_USER permission to the role so tests can access add user mutation
        cls.role.permissions.add(cls.perms["add_user"])
        cls.account_patcher_retrieve = patch(
            "users.strategies.users.accounts.internal_retrieve_user"
        )
        cls.mock_account_service_retrieve = cls.account_patcher_retrieve.start()
        cls.mock_account_service_retrieve.return_value = (
            None,
            {},
        )

    def setUp(self):
        super().setUp()
        self.mutation = """
        mutation AddUser($dataInput: AddUserInputType!) {
            addUser(dataInput: $dataInput) {
                user {
                    id
                    firstName
                    lastName
                    email
                    phone
                    activeStatus
                    role {
                        id
                        title
                        codename
                    }
                }
            }
        }
        """

    def test_add_user_success(self):
        """Test successful user creation"""
        # Create a role for the new user
        target_role = RoleFactory.create(
            title="Test User Role", permissions=[self.perms["view_organization"]]
        )
        self.organization.roles.add(target_role)
        account_patcher_create = patch(
            "users.strategies.users.accounts.internal_create_user"
        )
        mock_account_service_create = account_patcher_create.start()
        mock_account_service_create.return_value = {
            "pk": "12345",
            "email": "<EMAIL>",
            "phone": "+**********",
        }
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "email": "<EMAIL>",
                "phone": "+**********",
                "roleId": target_role.id,
                "firstName": "John",
                "lastName": "Doe",
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        # Verify user data
        user_data = result["data"]["addUser"]["user"]
        self.assertEqual(user_data["email"], "<EMAIL>")
        self.assertEqual(user_data["firstName"], "John")
        self.assertEqual(user_data["lastName"], "Doe")
        self.assertEqual(user_data["phone"], "+**********")
        self.assertEqual(
            user_data["activeStatus"], ActiveStatusChoices.WAITING.value.upper()
        )
        self.assertEqual(int(user_data["role"]["id"]), target_role.id)

        # Verify user was created in database
        created_user = User.objects.get(email="<EMAIL>")
        self.assertEqual(created_user.first_name, "John")
        self.assertEqual(created_user.last_name, "Doe")
        self.assertEqual(created_user.active_status, ActiveStatusChoices.WAITING)
        self.assertEqual(created_user.external_key, 12345)

    def test_add_user_unauthenticated(self):
        """Test add user mutation with unauthenticated user"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "email": "<EMAIL>",
                "phone": "+**********",
                "roleId": 1,
                "firstName": "John",
                "lastName": "Doe",
            }
        }

        result = self._execute_mutation(
            self.mutation, variables, context=self.non_auth_request
        )
        self._check_none_auth_error(result)

    def test_add_user_without_permission(self):
        """Test add user mutation without ADD_USER permission"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "email": "<EMAIL>",
                "phone": "+**********",
                "roleId": self.role.id,
                "firstName": "John",
                "lastName": "Doe",
            }
        }

        result = self._execute_mutation(
            self.mutation, variables, context=self.auth_request_no_perm
        )
        self._check_unauthorized_error(result)

    def test_add_user_invalid_organization(self):
        """Test add user mutation with invalid organization ID"""
        org_id = 99999
        variables = {
            "dataInput": {
                "orgId": org_id,  # Non-existent organization
                "email": "<EMAIL>",
                "phone": "+**********",
                "roleId": 1,
                "firstName": "John",
                "lastName": "Doe",
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "status": 404,
                    "status_text": "Not Found",
                    "reason": {"orgId": f"Organization with id {org_id} not found"},
                }
            },
        )

    def test_add_user_invalid_role(self):
        """Test add user mutation with invalid role ID"""
        role_id = 99999
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "email": "<EMAIL>",
                "phone": "+**********",
                "roleId": role_id,  # Non-existent role
                "firstName": "John",
                "lastName": "Doe",
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "status": 404,
                    "status_text": "Not Found",
                    "reason": {"roleId": f"Invalid role_id {role_id}."},
                }
            },
        )

    def test_add_user_invalid_email_format(self):
        """Test add user mutation with invalid email format"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                "email": "invalid-email",  # Invalid email format
                "phone": "+**********",
                "roleId": self.role.id,
                "firstName": "John",
                "lastName": "Doe",
            }
        }
        account_patcher_create = patch(
            "users.strategies.users.accounts.internal_create_user"
        )
        mock_account_service_create = account_patcher_create.start()
        mock_account_service_create.side_effect = ValidationError(
            {"errors": [{"email": "Invalid email format"}]}
        )
        result = self._execute_mutation(self.mutation, variables)
        self.assertDictEqual(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "status": 400,
                    "status_text": "Bad Request",
                    "reason": {"errors": ["Invalid email format"]},
                }
            },
        )

    def test_add_user_missing_required_fields(self):
        """Test add user mutation with missing required fields"""
        variables = {
            "dataInput": {
                "orgId": self.organization.id,
                # Missing email, phone, roleId, firstName, lastName
            }
        }

        result = self._execute_mutation(self.mutation, variables)
        self.assertIn("errors", result)
