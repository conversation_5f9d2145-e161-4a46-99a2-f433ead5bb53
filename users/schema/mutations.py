from random import randint

import graphene
from django.utils.translation import gettext_lazy as _
from gabbro.acl.models import Role
from gabbro.graphene import BadRequest, NotFound

from common.service import Service
from common.utils import (
    authentication_required,
    authorize_user,
    convert_to_slug,
    organization_required,
)
from organizations.perms_constants import ADD_ROLE, CHANGE_ROLE, DELETE_ROLE
from users.models import User
from workspaces.models import Workspace
from .input_object_types import (
    AddUserInputType,
    AssignWorkspaceUserPermissionsInputType,
    ChangeUserActiveStatusInputType,
    ChangeUserRoleInputType,
    DeleteRoleInputType,
    RoleInputType,
    UpdateRoleInputType,
)
from .object_types import RoleType, UserType
from ..models import ActiveStatusChoices
from ..permissions import AddUserPerms, ChangeUserPerms
from ..permissions.roles import AssignWorkspaceUserPerms, ChangeUserRolePerms
from ..serializers import RoleSerializer
from ..strategies import AddUserStrategy, ChangeUserActiveStatusStrategy
from ..strategies.roles import (
    AssignWorkspaceUserPermissionsStrategy,
    ChangeUserRoleStrategy,
)
from ..validators import (
    AddUserValidation,
    AssignWorkspaceUserPermissionsValidation,
    ChangeUserActiveStatusValidation,
    ChangeUserRoleValidation,
)


class AddUser(graphene.Mutation):
    user = graphene.Field(UserType)

    class Input:
        data_input = AddUserInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            validator=AddUserValidation(),
            perms=AddUserPerms(),
            strategy=AddUserStrategy(),
        )
        user_added = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return AddUser(user=user_added)


class ChangeUserActiveStatus(graphene.Mutation):
    user = graphene.Field(UserType)

    class Input:
        data_input = ChangeUserActiveStatusInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            validator=ChangeUserActiveStatusValidation(),
            perms=ChangeUserPerms(),
            strategy=ChangeUserActiveStatusStrategy(),
        )
        user_target = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return ChangeUserActiveStatus(user=user_target)


class CreateRole(graphene.Mutation):
    role = graphene.Field(RoleType)

    class Input:
        data_input = RoleInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        authorize_user(model_obj=organization, user=user, permission=ADD_ROLE)
        serializer = RoleSerializer(
            data={
                **data_input,
                "codename": f"{convert_to_slug(data_input['title'])}_code_{organization.id}_{user.id}_{randint(1, 1000)}",
            }
        )
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        permissions_list = serializer.validated_data.pop("permissions_list")
        role = serializer.save()
        role.permissions.set(permissions_list)
        organization.roles.add(role)
        setattr(info.context, "organization", organization)
        return CreateRole(role=role)


class ChangeUserRole(graphene.Mutation):
    user = graphene.Field(UserType)

    class Input:
        data_input = ChangeUserRoleInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            validator=ChangeUserRoleValidation(),
            perms=ChangeUserRolePerms(),
            strategy=ChangeUserRoleStrategy(),
        )
        user_target = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return ChangeUserRole(user=user_target)


class UpdateRole(graphene.Mutation):
    role = graphene.Field(RoleType)

    class Input:
        data_input = UpdateRoleInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        authorize_user(model_obj=organization, user=user, permission=CHANGE_ROLE)
        role_id = data_input.pop("role_id")
        role = organization.roles.filter(id=role_id).first()
        if not role:
            raise NotFound(
                reason={
                    "role_id": _("Invalid role_id %(role_id)s") % {"role_id": role_id}
                }
            )
        serializer = RoleSerializer(role, data=data_input, partial=True)
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        permissions_list = serializer.validated_data.pop("permissions_list", None)
        if permissions_list:
            role.permissions.set(permissions_list)
        serializer.save()
        setattr(info.context, "organization", organization)
        return UpdateRole(role=role)


class DeleteRole(graphene.Mutation):
    success = graphene.Boolean()

    class Input:
        data_input = DeleteRoleInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        authorize_user(model_obj=organization, user=user, permission=DELETE_ROLE)
        role_id = data_input["role_id"]
        role: Role = organization.roles.filter(id=role_id).first()
        if not role:
            raise NotFound(
                reason={
                    "role_id": _("Invalid role_id %(role_id)s") % {"role_id": role_id}
                }
            )

        # Delete all users' rules associated with the role
        users_per_role = organization.acl_users_per_role(role)
        for user in users_per_role:
            user.active_status = ActiveStatusChoices.DELETED
            organization.acl_remove_user(user=user)
            workspaces_for_user = Workspace.acl_objects_for_user(
                user=user, perms=[]
            ).distinct()
            for workspace in workspaces_for_user:
                workspace.acl_remove_user(user=user)
        User.objects.bulk_update(objs=users_per_role, fields=["active_status"])

        # Delete the role
        role.delete()
        return DeleteRole(success=True)


class AssignWorkspaceUserPermissions(graphene.Mutation):
    users = graphene.List(UserType)

    class Input:
        data_input = AssignWorkspaceUserPermissionsInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            validator=AssignWorkspaceUserPermissionsValidation(),
            perms=AssignWorkspaceUserPerms(),
            strategy=AssignWorkspaceUserPermissionsStrategy(),
        )
        users, workspace = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        setattr(info.context, "workspace", workspace)
        return AssignWorkspaceUserPermissions(users=users)


class Mutation(graphene.ObjectType):
    add_user = AddUser.Field()
    change_user_active_status = ChangeUserActiveStatus.Field()
    change_user_role = ChangeUserRole.Field()
    create_role = CreateRole.Field()
    update_role = UpdateRole.Field()
    delete_role = DeleteRole.Field()
    assign_workspace_user_permissions = AssignWorkspaceUserPermissions.Field()
