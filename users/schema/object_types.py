from collections import defaultdict

import graphene
from django.contrib.auth.models import Permission
from django.db.models import QuerySet
from gabbro.acl.models import Role
from graphene_django import DjangoObjectType

from users.models import User


class PermissionType(DjangoObjectType):
    class Meta:
        model = Permission
        fields = ("id", "name", "codename")


class GroupedPermissionType(graphene.ObjectType):
    title = graphene.String()
    permissions = graphene.List(PermissionType)


class PermissionModelType(graphene.ObjectType):
    model = graphene.String()
    grouped_permissions = graphene.List(GroupedPermissionType)


class RoleType(DjangoObjectType):
    users_count = graphene.Int()
    permissions = graphene.List(PermissionModelType)

    class Meta:
        model = Role
        fields = ("id", "title", "codename", "permissions")

    def resolve_users_count(self: Role, info):
        if organization := getattr(info.context, "organization", None):
            return organization.acl_users_per_role(self).count()
        return 0

    def resolve_permissions(self: Role, info):
        return group_model_permissions(self.permissions.all())


class RoleListType(graphene.ObjectType):
    data = graphene.List(RoleType)
    count = graphene.Int()


class WorkspacePermissionType(DjangoObjectType):
    class Meta:
        model = Role
        fields = ("id", "title", "codename")


class UserType(DjangoObjectType):
    id = graphene.Int()
    role = graphene.Field(RoleType)
    workspace_permissions = graphene.List(WorkspacePermissionType)

    class Meta:
        model = User
        fields = (
            "id",
            "first_name",
            "last_name",
            "email",
            "phone",
            "is_superuser",
            "is_staff",
            "active_status",
            "avatar",
            "role",
            "workspace_permissions",
        )

    def resolve_role(self: User, info):
        if organization := getattr(info.context, "organization", None):
            return organization.acl_user_roles(self).first()
        return None

    def resolve_workspace_permissions(self: User, info):
        if workspace := getattr(info.context, "workspace", None):
            return workspace.acl_user_roles(self)
        return None


class UserListType(graphene.ObjectType):
    data = graphene.List(UserType)
    count = graphene.Int()


def group_model_permissions(
    permissions: QuerySet[Permission], list_all_permissions=False
):
    admin = defaultdict(list)
    viewer = defaultdict(list)
    editor = defaultdict(list)
    model_grouped_permissions = defaultdict(list)
    models_set = set()
    for permission in permissions:
        perm, model = permission.codename.split("_")
        models_set.add(model)
        if perm == "view":
            admin[model].append(permission)
            editor[model].append(permission)
            viewer[model].append(permission)
        elif perm == "add":
            admin[model].append(permission)
            editor[model].append(permission)
        elif perm == "change":
            admin[model].append(permission)
            editor[model].append(permission)
        elif perm == "delete":
            admin[model].append(permission)

    if list_all_permissions:
        for model in models_set:
            if admin.get(model) and len(admin[model]) == 4:
                model_grouped_permissions[model].append(
                    {"title": "admin", "permissions": admin[model]}
                )
            if viewer.get(model) and len(viewer[model]) == 1:
                model_grouped_permissions[model].append(
                    {"title": "viewer", "permissions": viewer[model]}
                )
            if editor.get(model) and len(editor[model]) == 3:
                model_grouped_permissions[model].append(
                    {"title": "editor", "permissions": editor[model]}
                )
    else:
        for model in models_set:
            if admin.get(model) and len(admin[model]) == 4:
                model_grouped_permissions[model].append(
                    {"title": "admin", "permissions": admin[model]}
                )
            elif viewer.get(model) and len(viewer[model]) == 1:
                model_grouped_permissions[model].append(
                    {"title": "viewer", "permissions": viewer[model]}
                )
            elif editor.get(model) and len(editor[model]) == 3:
                model_grouped_permissions[model].append(
                    {"title": "editor", "permissions": editor[model]}
                )
    grouped_permissions = [
        {"model": model, "grouped_permissions": grouped_permissions}
        for model, grouped_permissions in model_grouped_permissions.items()
    ]
    return grouped_permissions
