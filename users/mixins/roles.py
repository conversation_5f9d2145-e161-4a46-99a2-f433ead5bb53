from django.utils.translation import gettext_lazy as _
from gabbro.graphene import NotFound

from organizations.models import Organization


class RoleMixin:
    @staticmethod
    def get_role_if_exists(role_id: int, organization: Organization):
        role = organization.roles.filter(id=role_id).first()
        if not role:
            raise NotFound(
                reason={
                    "role_id": _("Invalid role_id %(role_id)s.") % {"role_id": role_id}
                }
            )
        return role
