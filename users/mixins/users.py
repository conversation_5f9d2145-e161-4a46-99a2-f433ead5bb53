from django.utils.translation import gettext_lazy as _
from gabbro.graphene import NotFound

from organizations.models import Organization


class UserMixin:
    @staticmethod
    def get_organization_user_if_exists(user_id: int, organization: Organization):
        user = organization.acl_individuals.filter(id=user_id).first()
        if not user:
            raise NotFound(
                reason={
                    "user_id": _("User %(user_id)s not in organization %(org_id)s")
                    % {"org_id": organization.id, "user_id": user_id}
                }
            )
        return user
