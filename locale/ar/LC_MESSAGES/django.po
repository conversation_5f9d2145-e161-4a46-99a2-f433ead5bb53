# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-26 21:54+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 "
"&& n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#: app/auth.py:20 app/auth.py:65
msgid "INVALID TOKEN"
msgstr "رمز غير صالح"

#: app/storage_backends.py:26
msgid "MEDIA_URL has not been configured"
msgstr "لم يتم تكوين MEDIA_URL"

#: app/storage_backends.py:40
msgid "STATIC_URL has not been configured"
msgstr "لم يتم تكوين STATIC_URL"

#: app/urls.py:30
msgid "Geo Core Admin"
msgstr "إدارة جيو كور"

#: app/urls.py:31
msgid "Geo Core Admin Portal"
msgstr "بوابة إدارة جيو كور"

#: app/urls.py:32
msgid "Welcome to Geo Core Admin"
msgstr "مرحبًا بك في إدارة جيو كور"

#: chat_ai/models/conversation.py:10
msgid "UUID PK"
msgstr "UUID المفتاح الأساسي"

#: chat_ai/models/conversation.py:13
msgid "layers"
msgstr "الطبقات"

#: chat_ai/models/conversation.py:19
msgid "user"
msgstr "المستخدم"

#: chat_ai/models/conversation.py:23
msgid "Conversation"
msgstr "المحادثة"

#: chat_ai/models/conversation.py:24
msgid "Conversations"
msgstr "المحادثات"

#: chat_ai/models/message.py:12
msgid "conversation"
msgstr "المحادثة"

#: chat_ai/models/message.py:15
msgid "message"
msgstr "الرسالة"

#: chat_ai/models/message.py:16
msgid "contains user input, GPT output, and SQL Query"
msgstr "يتضمن إدخال المستخدم، مخرجات GPT، واستعلام SQL"

#: chat_ai/models/message.py:20
msgid "Message"
msgstr "الرسالة"

#: chat_ai/models/message.py:21
msgid "Messages"
msgstr "الرسائل"

#: chat_ai/schema/mutation.py:60
msgid "your question is too long, try to short it"
msgstr "سؤالك طويل جدًا، يرجى تقصيره"

#: chat_ai/schema/utils.py:16
#: chat_ai/tests/test_schema/test_mutations/test_chat_ai.py:89
#: chat_ai/tests/test_schema/test_mutations/test_reset_chat_ai.py:68
#: dynamic_layers/schema/query.py:185 dynamic_layers/schema/query.py:220
#: layers/schema/query.py:64
#: layers/tests/test_schema/test_queries/test_layers_advanced_filters.py:407
#: workspaces/schema/query.py:262
#: workspaces/tests/test_schema/test_queries/test_eda_reports.py:219
msgid "Invalid workspace_id"
msgstr "معرف مساحة العمل غير صالح"

#: chat_ai/schema/utils.py:26
#: chat_ai/tests/test_schema/test_mutations/test_chat_ai.py:105
#: chat_ai/tests/test_schema/test_mutations/test_reset_chat_ai.py:84
msgid "No layers found with the provided IDs"
msgstr "لم يتم العثور على أي طبقات باستخدام المعرفات المقدمة"

#: chat_ai/tests/test_schema/test_mutations/test_chat_ai.py:57
#: chat_ai/tests/test_schema/test_mutations/test_reset_chat_ai.py:53
#: layers/tests/test_schema/test_mutations/test_create_iso_metadata.py:56
#: layers/tests/test_schema/test_mutations/test_update_layer_iso_metadata.py:60
#: layers/tests/test_schema/test_queries/test_layers_advanced_filters.py:85
#: layers/tests/test_schema/test_queries/test_records_advanced_filters.py:120
#: workspaces/tests/test_schema/test_mutations/test_cancel_dataset_request.py:54
#: workspaces/tests/test_schema/test_mutations/test_cancel_workspace_request.py:54
#: workspaces/tests/test_schema/test_mutations/test_create_dataset_request.py:61
#: workspaces/tests/test_schema/test_mutations/test_create_design_layer_request.py:56
#: workspaces/tests/test_schema/test_mutations/test_create_location_field_mapping.py:74
#: workspaces/tests/test_schema/test_mutations/test_create_log_field_mapping.py:79
#: workspaces/tests/test_schema/test_mutations/test_create_workspace.py:150
#: workspaces/tests/test_schema/test_mutations/test_create_workspace_request.py:60
#: workspaces/tests/test_schema/test_mutations/test_delete_workspace.py:84
#: workspaces/tests/test_schema/test_mutations/test_design_layer_json_schema.py:80
#: workspaces/tests/test_schema/test_mutations/test_update_design_layer_request.py:71
#: workspaces/tests/test_schema/test_mutations/test_update_json_schema.py:69
#: workspaces/tests/test_schema/test_mutations/test_update_json_schemas.py:72
#: workspaces/tests/test_schema/test_mutations/test_update_workspace.py:70
#: workspaces/tests/test_schema/test_queries/test_data_set_sample.py:70
#: workspaces/tests/test_schema/test_queries/test_dataset_request.py:62
#: workspaces/tests/test_schema/test_queries/test_datasets.py:74
#: workspaces/tests/test_schema/test_queries/test_eda_reports.py:94
#: workspaces/tests/test_schema/test_queries/test_json_schemas.py:74
#: workspaces/tests/test_schema/test_queries/test_workspace_requests.py:82
#: workspaces/tests/test_schema/test_queries/test_workspaces.py:40
msgid "Unauthorized"
msgstr "غير مصرح"

#: common/apps.py:8
msgid "Common"
msgstr "عام"

#: common/handlers/dataset_files.py:56 workspaces/schema/utils.py:30
msgid "Invalid dataset file"
msgstr "ملف مجموعة البيانات غير صالح"

#: common/handlers/dataset_files.py:75
#, python-format
msgid "Error loading data: %(e)s"
msgstr "خطأ في تحميل البيانات: %(e)s"

#: common/handlers/dataset_files.py:108
#, python-format
msgid "Unsupported file format: %(file_extension)s"
msgstr "تنسيق الملف غير مدعوم: %(file_extension)s"

#: common/handlers/dataset_files.py:195
msgid "Invalid ZIP file"
msgstr "ملف ZIP غير صالح"

#: common/handlers/dataset_files.py:197
#, python-format
msgid "Failed to read ZIP: %(e)s"
msgstr "فشل في قراءة ZIP: %(e)s"

#: common/handlers/dataset_files.py:200
msgid "ZIP is empty"
msgstr "الملف ZIP فارغ"

#: common/handlers/dataset_files.py:236
#, python-format
msgid ""
"No complete Shapefile found (need .shp, .shx, .dbf with same stem in same "
"folder). \n"
"Found groups: %(found)s"
msgstr "لم يتم العثور على ملف Shapefile كامل (يحتاج إلى ملفات .shp، .shx، .dbf بنفس الجذع في نفس المجلد). \n"
"تم العثور على المجموعات: %(found)s"

#: common/handlers/dataset_files.py:250
#, python-format
msgid ""
"ZIP contains multiple Shapefile sets; exactly one is required. \n"
"Detected sets: %(stems_readable)s"
msgstr "يحتوي ملف ZIP على مجموعات Shapefile متعددة؛ مطلوب مجموعة واحدة فقط. \n"
"المجموعات المكتشفة: %(stems_readable)s"

#: common/handlers/dataset_files.py:271
#, python-format
msgid "Fiona could not open the Shapefile inside the ZIP: %(e)s"
msgstr "لم تتمكن فيونا من فتح Shapefile داخل ملف ZIP: %(e)s"

#: common/handlers/dataset_files.py:295
#, python-format
msgid ""
"Unexpected ZIP contents. Expected exactly one top-level .shp entry.\n"
"Found: %(stems)s\n"
"Ensure `validate_shapefile_zip` is called before this function."
msgstr "محتوى ZIP غير متوقع. من المتوقع وجود إدخال .shp واحد فقط من المستوى الأعلى.\n"
"وجد: %(stems)s\n"
"تأكد من استدعاء `validate_shapefile_zip` قبل هذه الوظيفة."

#: common/utils/eda_generator.py:69
#, python-format
msgid "File does not exist! %(file_path)s"
msgstr "الملف غير موجود! %(file_path)s"

#: common/utils/geometry.py:22
msgid "Invalid geometry format"
msgstr "تنسيق مجموعة الاحداثيات غير صالح"

#: common/utils/geometry.py:26
msgid "Invalid geometry: Not a valid geometry object."
msgstr "مجموعة الاحداثيات غير صالحة"

#: common/utils/geometry.py:85
msgid "Input geometry is not a GeometryCollection"
msgstr "مجموعة الاحداثيات غير صالحة."

#: common/utils/graphene/decorators.py:20
msgid "Your account is deleted"
msgstr "تم حذف حسابك"

#: common/utils/graphene/decorators.py:22
msgid "Your account is inactive"
msgstr "حسابك غير نشط"

#: common/utils/graphene/decorators.py:41
#, python-format
msgid "Organization with id %(org_id)s not found"
msgstr "لم يتم العثور على المنظمة بالمعرف %(org_id)s"

#: common/utils/graphene/decorators.py:52 users/mixins/users.py:27
#, python-format
msgid "User %(user_id)s not in organization %(org_id)s"
msgstr "المستخدم %(user_id)s ليس في المنظمة %(org_id)s"

#: common/utils/graphene/decorators.py:64
#: common/utils/graphene/decorators.py:78 layers/permissions/record.py:16
msgid "Permission denied"
msgstr "تم رفض الإذن"

#: common/utils/graphene/mutations.py:13
msgid "GCS signed urls wrong configurations"
msgstr "إعدادات روابط GCS الموقعة غير صحيحة"

#: common/utils/graphene/query.py:58
msgid "Value provided is not an integer"
msgstr "القيمة المقدمة ليست عددًا صحيحًا"

#: common/utils/graphene/query.py:74
#, python-format
msgid "Value must be bigger than %(min_value)s"
msgstr "يجب أن تكون القيمة أكبر من %(min_value)s"

#: common/utils/graphene/query.py:178
#, python-format
msgid "invalid %(order_by)s field name"
msgstr "اسم الحقل %(order_by)s غير صالح"

#: common/utils/graphene/scalars.py:30
#, python-format
msgid "Value %(value)s is not JSON serializable"
msgstr "القيمة %(value)s لا يمكن تحويلها إلى تنسيق JSON"

#: common/utils/json_schema.py:82
#, python-format
msgid "JsonSchema Validation Error: %(message)s"
msgstr "خطأ في التحقق من صحة JsonSchema: %(message)s"

#: common/utils/models.py:14
#, python-format
msgid ""
"Unsupported file format: %(extension)s. Allowed extensions are: "
"%(allowed_extensions)s"
msgstr ""
"تنسيق الملف غير مدعوم: %(extension)s. الامتدادات المسموح بها هي: "
"%(allowed_extensions)s"

#: dynamic_layers/models/dynamic_layer.py:12 layers/models/layer.py:27
msgid "Published"
msgstr "منشور"

#: dynamic_layers/models/dynamic_layer.py:13 layers/models/layer.py:28
msgid "Unpublished"
msgstr "غير منشور"

#: dynamic_layers/models/dynamic_layer.py:23 layers/models/iso_metadata.py:51
#: layers/models/layer.py:38 workspaces/models/dataset.py:27
#: workspaces/models/request.py:52
msgid "Dataset"
msgstr "مجموعة البيانات"

#: dynamic_layers/models/dynamic_layer.py:29 layers/models/layer.py:44
#: workspaces/models/workspace.py:72
msgid "Workspace"
msgstr "مساحة العمل"

#: dynamic_layers/models/dynamic_layer.py:32 layers/models/layer.py:47
#: layers/models/layer.py:165
msgid "SLDs"
msgstr "SLDs"

#: dynamic_layers/models/dynamic_layer.py:35 layers/models/layer.py:50
msgid "Unique Form Key"
msgstr "مفتاح النموذج الفريد"

#: dynamic_layers/models/dynamic_layer.py:37 layers/models/layer.py:52
msgid "Layer Title"
msgstr "عنوان الطبقة"

#: dynamic_layers/models/dynamic_layer.py:39 layers/models/layer.py:54
msgid "Layer Description"
msgstr "وصف الطبقة"

#: dynamic_layers/models/dynamic_layer.py:43 layers/models/layer.py:58
msgid "Read Only"
msgstr "للقراءة فقط"

#: dynamic_layers/models/dynamic_layer.py:44 layers/models/layer.py:59
msgid "Is this layer editable or not?"
msgstr "هل هذه الطبقة قابلة للتحرير أم لا؟"

#: dynamic_layers/models/dynamic_layer.py:51 layers/models/layer.py:66
msgid "Layer Status"
msgstr "حالة الطبقة"

#: dynamic_layers/models/dynamic_layer.py:54 layers/models/iso_metadata.py:15
#: layers/models/layer.py:69
msgid "Boundaries"
msgstr "الحدود"

#: dynamic_layers/models/dynamic_layer.py:57 layers/models/layer.py:72
msgid "Location Field Mapping"
msgstr "تعيين حقل الموقع"

#: dynamic_layers/models/dynamic_layer.py:65 layers/models/layer.py:80
msgid "JSON Schema"
msgstr "مخطط JSON"

#: dynamic_layers/models/dynamic_layer.py:66 layers/models/layer.py:81
msgid "Form and UI Schemas"
msgstr "مخططات النموذج وواجهة المستخدم"

#: dynamic_layers/models/dynamic_layer.py:71 layers/models/layer.py:86
msgid "Layer Web UI JsonSchema"
msgstr "واجهة الويب للطبقة مخطط JSON"

#: dynamic_layers/models/dynamic_layer.py:76 layers/models/layer.py:91
#: workspaces/models/request.py:76
msgid "Layer Data"
msgstr "بيانات الطبقة"

#: dynamic_layers/models/dynamic_layer.py:77 layers/models/layer.py:92
msgid "Layer extra data"
msgstr "بيانات إضافية للطبقة"

#: dynamic_layers/models/dynamic_layer.py:80 layers/models/layer.py:95
msgid "Records Last Modified"
msgstr "آخر تعديل للسجلات"

#: dynamic_layers/models/dynamic_layer.py:85 layers/models/layer.py:100
msgid "Layer Filters"
msgstr "مرشحات الطبقة"

#: dynamic_layers/models/dynamic_layer.py:86 layers/models/layer.py:101
msgid "Layer filters"
msgstr "مرشحات الطبقة"

#: dynamic_layers/models/dynamic_layer.py:92
msgid "Dynamic Table Name"
msgstr "اسم الجدول الديناميكي"

#: dynamic_layers/models/dynamic_layer.py:93
msgid "Name of the dynamically created table for this layer"
msgstr "اسم الجدول الذي تم إنشاؤه ديناميكيًا لهذه الطبقة"

#: dynamic_layers/models/dynamic_layer.py:97
msgid "Dynamic Layer"
msgstr "الطبقة الديناميكية"

#: dynamic_layers/models/dynamic_layer.py:98
msgid "Dynamic Layers"
msgstr "الطبقات الديناميكية"

#: dynamic_layers/schema/query.py:136 layers/schema/query.py:98
msgid "Invalid layer_id"
msgstr "معرّف الطبقة غير صالح"

#: dynamic_layers/schema/query.py:169
msgid "Failed to query dynamic records"
msgstr "فشل في الاستعلام عن السجلات الديناميكية"

#: layers/models/iso_metadata.py:7 workspaces/models/dataset.py:22
msgid "Title"
msgstr "العنوان"

#: layers/models/iso_metadata.py:8 workspaces/models/workspace.py:42
msgid "Description"
msgstr "الوصف"

#: layers/models/iso_metadata.py:9
msgid "Dataset Creation Date"
msgstr "تاريخ إنشاء مجموعة البيانات"

#: layers/models/iso_metadata.py:10
msgid "Tags"
msgstr "العلامات"

#: layers/models/iso_metadata.py:12
msgid "Projection"
msgstr "الإسقاط"

#: layers/models/iso_metadata.py:18
msgid "Responsible Authority Name"
msgstr "اسم السلطة المسؤولة"

#: layers/models/iso_metadata.py:21
msgid "Responsible Authority Role"
msgstr "دور السلطة المسؤولة"

#: layers/models/iso_metadata.py:24
msgid "Responsible Authority Phone"
msgstr "هاتف السلطة المسؤولة"

#: layers/models/iso_metadata.py:27
msgid "Responsible Authority Email"
msgstr "البريد الإلكتروني للسلطة المسؤولة"

#: layers/models/iso_metadata.py:42 layers/models/layer.py:105
#: workspaces/models/request.py:60
msgid "Layer"
msgstr "طبقة"

#: layers/models/layer.py:12
msgid "Points"
msgstr "نقاط"

#: layers/models/layer.py:13
msgid "Heatmap"
msgstr "الخريطة الحرارية"

#: layers/models/layer.py:17
msgid "Point"
msgstr "نقطة"

#: layers/models/layer.py:18
msgid "Multi Point"
msgstr "متعدد النقاط"

#: layers/models/layer.py:19
msgid "Polygon"
msgstr "مضلع"

#: layers/models/layer.py:20
msgid "Multi polygon"
msgstr "متعدد المضلعات"

#: layers/models/layer.py:21
msgid "Linestring"
msgstr "سلسلة الخط"

#: layers/models/layer.py:22
msgid "Multi Line String"
msgstr "سلسلة متعددة الخطوط"

#: layers/models/layer.py:23
msgid "Geometry Collection"
msgstr "مجموعة الاحداثيات"

#: layers/models/layer.py:106
msgid "Layers"
msgstr "الطبقات"

#: layers/models/layer.py:151
msgid "SLD Title"
msgstr "SLD عنوان"

#: layers/models/layer.py:156
msgid "SLD Type"
msgstr "نوع SLD"

#: layers/models/layer.py:159
msgid "SLD Data"
msgstr "بيانات SLD"

#: layers/models/layer.py:161
msgid "XML Body"
msgstr "XML Body"

#: layers/models/layer.py:164
msgid "SLD"
msgstr "SLD"

#: layers/models/record.py:14
msgid "Related Layer"
msgstr "الطبقة المرتبطة"

#: layers/models/record.py:19
msgid "Geometry Collection Record"
msgstr "سجل مجموعة مجموعة الاحداثيات"

#: layers/models/record.py:24
msgid "Buffer Geometry"
msgstr "هندسة العازل"

#: layers/models/record.py:29
msgid "Source Properties"
msgstr "خصائص المصدر"

#: layers/models/record.py:34
msgid "Map Data"
msgstr "بيانات الخريطة"

#: layers/models/record.py:39
msgid "Order Data Dependency"
msgstr "اعتماد بيانات الطلب"

#: layers/models/record.py:44
msgid "Weight"
msgstr "الوزن"

#: layers/models/record.py:45
msgid "the weight values from the data field that affects on heatmap"
msgstr "قيم الوزن من حقل البيانات التي تؤثر على الخريطة الحرارية"

#: layers/models/record.py:49
msgid "Geometry Record"
msgstr "سجل مجموعة الاحداثيات"

#: layers/models/record.py:50
msgid "Geometry Records"
msgstr "سجلات مجموعة الاحداثيات"

#: layers/scripts/publish_layer_to_geoserver.py:80
#: layers/scripts/publish_layer_to_geoserver.py:103
msgid "Layer is required"
msgstr "هذا الحقل مطلوب."

#: layers/strategies/layer.py:217
msgid "No data found in the specified range."
msgstr "لم يتم العثور على بيانات في النطاق المحدد"

#: layers/validators/iso_metadata.py:21
msgid "Invalid layer_id: %(layer_id)s"
msgstr "معرّف الطبقة %(layer_id)s غير صالح."

#: layers/validators/iso_metadata.py:32
msgid "Invalid iso_metadata_id %(iso_metadata_id)s"
msgstr "معرّف بيانات التعريف %(iso_metadata_id)s غير صالح."

#: layers/validators/layer.py:46
#, python-format
msgid "columns %(columns)s not included in the layer json schema"
msgstr "الأعمدة %(columns)s غير مدمجة في مصفوفة البيانات"

#: layers/validators/record.py:51
#, python-format
msgid "Record with id %(record_id)s not found"
msgstr "لم يتم العثور على السجل بالمعرف %(record_id)s"

#: layers/views.py:18
msgid "Your are not authorized to view this page."
msgstr "ليس لديك إذن لعرض هذه الصفحة."

#: layers/views.py:27
#, python-format
msgid "Can't publish layer %(pk)s because the layer is already published."
msgstr "لا يمكن نشر الطبقة %(pk)s لأن الطبقة قد تم نشرها بالفعل."

#: layers/views.py:37
#, python-format
msgid "Layer %(layer)s is published"
msgstr "الطبقة %(layer)s تم نشرها"

#: organizations/apps.py:7 organizations/models.py:34
msgid "Organizations"
msgstr "المنظمات"

#: organizations/models.py:24
msgid "Roles"
msgstr "الأدوار"

#: organizations/models.py:28
msgid "Workspaces Data"
msgstr "بيانات المساحات العمل"

#: organizations/models.py:33 workspaces/models/dataset.py:15
#: workspaces/models/request.py:44 workspaces/models/workspace.py:38
msgid "Organization"
msgstr "منظمة"

#: organizations/perms_constants.py:35
msgid "Can view user"
msgstr "يمكنه عرض المستخدم"

#: organizations/perms_constants.py:36
msgid "Can add user"
msgstr "يمكنه إضافة مستخدم"

#: organizations/perms_constants.py:37
msgid "Can change user"
msgstr "يمكنه تعديل المستخدم"

#: organizations/perms_constants.py:38
msgid "Can delete user"
msgstr "يمكنه حذف المستخدم"

#: organizations/perms_constants.py:40
msgid "Can view layer"
msgstr "يمكنه عرض الطبقة"

#: organizations/perms_constants.py:41
msgid "Can add layer"
msgstr "يمكنه إضافة طبقة"

#: organizations/perms_constants.py:42
msgid "Can delete layer"
msgstr "يمكنه حذف الطبقة"

#: organizations/perms_constants.py:43
msgid "Can edit layer"
msgstr "يمكنه تعديل الطبقة"

#: organizations/perms_constants.py:45
msgid "Can view record"
msgstr "يمكنه عرض السجل"

#: organizations/perms_constants.py:46
msgid "Can add record"
msgstr "يمكنه إضافة سجل"

#: organizations/perms_constants.py:47
msgid "Can delete record"
msgstr "يمكنه حذف السجل"

#: organizations/perms_constants.py:48
msgid "Can edit record"
msgstr "يمكنه تعديل السجل"

#: organizations/perms_constants.py:50
msgid "Can add workspace"
msgstr "تستطيع اضافة مساحة عمل"

#: organizations/perms_constants.py:51
msgid "Can change workspace"
msgstr "يمكنه تعديل مساحة العمل"

#: organizations/perms_constants.py:52
msgid "Can delete workspace"
msgstr "يمكنه حذف مساحة العمل"

#: organizations/perms_constants.py:53
msgid "Can view workspace"
msgstr "يمكنه عرض مساحة العمل"

#: organizations/perms_constants.py:55
msgid "Can add role"
msgstr "يمكنه إضافة دور"

#: organizations/perms_constants.py:56
msgid "Can change role"
msgstr "يمكنه تعديل دور"

#: organizations/perms_constants.py:57
msgid "Can delete role"
msgstr "يمكنه حذف دور"

#: organizations/perms_constants.py:58
msgid "Can view role"
msgstr "يمكنه عرض دور"

#: proxy/views.py:40
msgid "Authentication Failed"
msgstr "دخول خاطئ"

#: proxy/views.py:64
#, python-format
msgid "Invalid workspace_id %(workspace_id)s"
msgstr "مساحة العمل %(workspace_id)s غير صالحة"

#: proxy/views.py:75
msgid "You are not authorized to access this workspace"
msgstr "ليس لديك إذن لعرض مساحة العمل."

#: users/apps.py:7
msgid "Users"
msgstr "المستخدمون"

#: users/mixins/roles.py:14
#, python-format
msgid "Invalid role_id %(role_id)s."
msgstr "معرّف الدور %(role_id)s غير صالح."

#: users/mixins/users.py:15
#, python-format
msgid "User with id %(user_id)s not found"
msgstr "المستخدم الذي بالمعرف %(user_id)s غير موجود"

#: users/models.py:7
msgid "Active"
msgstr "نشط"

#: users/models.py:8
msgid "Inactive"
msgstr "غير نشط"

#: users/models.py:9
msgid "Waiting"
msgstr "قيد الانتظار"

#: users/models.py:10
msgid "Deleted"
msgstr "محذوف"

#: users/models.py:18
msgid "active status"
msgstr "حالة النشاط"

#: users/schema/mutations.py:158 users/schema/mutations.py:185
msgid "Invalid role_id"
msgstr "معرّف الدور غير صالح"

#: users/schema/query.py:97
msgid "No owner user found"
msgstr "لم يتم العثور على مستخدم مالك"

#: users/validators/roles.py:38
#, python-format
msgid "Invalid workspace %(workspace_id)s"
msgstr "مساحة العمل %(workspace_id)s غير صالحة"

#: users/validators/roles.py:49
#, python-format
msgid "Invalid users %(users_ids)s"
msgstr "معرّفات المستخدمين %(users_ids)s غير صالحة"

#: users/validators/roles.py:63
#, python-format
msgid "Invalid permissions %(permissions_ids)s"
msgstr "معرّفات الصلاحيات %(permissions_ids)s غير صالحة"

#: users/validators/user.py:29
msgid "Invalid user_id"
msgstr "معرّف المستخدم غير صالح"

#: users/validators/user.py:37
msgid "User is waiting, you can't change activation status"
msgstr "المستخدم قيد الانتظار، لا يمكنك تغيير حالة التفعيل"

#: users/validators/user.py:43
msgid "Can't set activation to waiting"
msgstr "لا يمكن تعيين حالة التفعيل إلى قيد الانتظار"

#: workspaces/admin/eda_report.py:28
msgid "Report Link"
msgstr "رابط التقرير"

#: workspaces/apps.py:8 workspaces/models/workspace.py:73
msgid "Workspaces"
msgstr "المساحات العمل"

#: workspaces/mixins/dataset.py:34
#: workspaces/tests/test_schema/test_mutations/test_create_dataset_request.py:93
#, python-format
msgid "Dataset file extension must be on of %(extension)s"
msgstr "امتداد ملف مجموعة البيانات يجب أن يكون من نوع %(extension)s"

#: workspaces/mixins/dataset.py:48 workspaces/strategies/request.py:159
#: workspaces/strategies/request.py:177
#: workspaces/tests/test_schema/test_mutations/test_create_log_field_mapping.py:138
#: workspaces/tests/test_schema/test_mutations/test_create_log_field_mapping.py:380
#, python-format
msgid "columns %(columns)s not included in the dataset"
msgstr "الأعمدة %(columns)s غير مدمجة في مجموعة البيانات"

#: workspaces/mixins/dataset.py:59
#, python-format
msgid "Dataset with %(dataset_id)s not found"
msgstr "مجموعة البيانات التي بالمعرف %(dataset_id)s غير موجودة"

#: workspaces/mixins/request.py:34
#, python-format
msgid "You have %(request_type)s request in progress."
msgstr "لديك طلب من نوع %(request_type)s قيد المعالجة."

#: workspaces/mixins/request.py:67
#, python-format
msgid "Dataset with %(workspace_request_id)s not found"
msgstr "لم يتم العثور على مجموعة البيانات بالمعرّف %(workspace_request_id)s"

#: workspaces/mixins/request.py:81 workspaces/permissions/workspace.py:51
msgid "Permission denied, you do not have permission to this action."
msgstr "تم رفض الإذن، ليس لديك إذن للقيام بهذا الإجراء."

#: workspaces/mixins/workspace.py:17
#, python-format
msgid "Workspace with %(workspace_id)s not found"
msgstr "المساحة العمل التي بالمعرف %(workspace_id)s غير موجودة"

#: workspaces/models/dataset.py:19
msgid "Original Dataset File"
msgstr "ملف مجموعة البيانات الأصلية"

#: workspaces/models/dataset.py:24
msgid "Meta Data"
msgstr "البيانات الوصفية"

#: workspaces/models/dataset.py:28 workspaces/models/workspace.py:67
msgid "Datasets"
msgstr "مجموعة البيانات"

#: workspaces/models/eda_report.py:7
msgid "YData"
msgstr "واي داتا"

#: workspaces/models/eda_report.py:8
msgid "SweetViz"
msgstr "سويت فيز"

#: workspaces/models/eda_report.py:18
msgid "dataset"
msgstr "مجموعة البيانات"

#: workspaces/models/eda_report.py:20
msgid "hash code"
msgstr "كود التشفير"

#: workspaces/models/eda_report.py:21
msgid "file"
msgstr "الملف"

#: workspaces/models/eda_report.py:23
msgid "sources"
msgstr "المصادر"

#: workspaces/models/eda_report.py:27
msgid "EDA Report"
msgstr "تقرير التحليل الاستكشافي"

#: workspaces/models/eda_report.py:28
msgid "EDA Reports"
msgstr "تقارير التحليل الاستكشافي"

#: workspaces/models/request.py:20
msgid "In Progress"
msgstr "قيد التنفيذ"

#: workspaces/models/request.py:21
msgid "Finished"
msgstr "منجز"

#: workspaces/models/request.py:22
msgid "Cancelled"
msgstr "ملغى"

#: workspaces/models/request.py:26
msgid "Upload File"
msgstr "رفع ملف"

#: workspaces/models/request.py:27
msgid "Design Layer"
msgstr "تصميم طبقة"

#: workspaces/models/request.py:28
msgid "Connect Database"
msgstr "ربط قاعدة البيانات"

#: workspaces/models/request.py:36
msgid "Created by"
msgstr "تم الإنشاء بواسطة"

#: workspaces/models/request.py:67
msgid "Request Status"
msgstr "حالة الطلب"

#: workspaces/models/request.py:74
msgid "Request Type"
msgstr "نوع الطلب"

#: workspaces/models/request.py:80
msgid "Dataset Request"
msgstr "طلب مجموعة البيانات"

#: workspaces/models/request.py:81
msgid "Dataset Requests"
msgstr "طلبات مجموعة البيانات"

#: workspaces/models/workspace.py:30
msgid "Owner"
msgstr "المالك"

#: workspaces/models/workspace.py:40
msgid "Name"
msgstr "الاسم"

#: workspaces/models/workspace.py:45
msgid "Map Thumbnail"
msgstr "صورة مصغرة للخريطة"

#: workspaces/models/workspace.py:48
msgid "Last Visited"
msgstr "آخر زيارة"

#: workspaces/models/workspace.py:51
msgid "Layers Sorted IDs"
msgstr "معرفات الطبقات مرتبة"

#: workspaces/models/workspace.py:54
msgid "Layers Data"
msgstr "بيانات الطبقات"

#: workspaces/models/workspace.py:61
msgid "Workspace Type"
msgstr "نوع مساحة العمل"

#: workspaces/schema/query.py:147 workspaces/schema/query.py:193
#: workspaces/tests/test_schema/test_queries/test_data_set_sample.py:129
#: workspaces/tests/test_schema/test_queries/test_json_schemas.py:142
#, python-format
msgid "Invalid dataset_request_id: %(id)s"
msgstr "معرف طلب مجموعة البيانات غير صالح: %(id)s"

#: workspaces/schema/query.py:230 workspaces/schema/query.py:300
#: workspaces/tests/test_schema/test_queries/test_datasets.py:195
#, python-format
msgid "Workspace with id %(workspace_id)s not found"
msgstr "مساحة العمل بالمعرف %(workspace_id)s غير موجودة"

#: workspaces/schema/query.py:272
#: workspaces/tests/test_schema/test_queries/test_eda_reports.py:244
#, python-format
msgid "Layer with id %(layer_id)s not found"
msgstr "الطبقة بالمعرف %(layer_id)s غير موجودة"

#: workspaces/serializers/request.py:67
msgid ""
"lat_lon_column_num cannot be 'two_column' when coordinate_type is 'other'"
msgstr ""
"عدد عمود الإحداثيات (خط العرض والطول) لا يمكن أن يكون 'two_column' عندما "
"يكون نوع الإحداثيات 'آخر'"

#: workspaces/serializers/request.py:75 workspaces/serializers/request.py:82
#: workspaces/serializers/request.py:83
msgid "This field is required."
msgstr "هذا الحقل مطلوب."

#: workspaces/strategies/request.py:203
#, python-format
msgid "Invalid geometry columns %(columns)s"
msgstr "أعمدة مجموعة الاحداثيات غير صالحة %(columns)s"

#: workspaces/tests/test_schema/test_mutations/test_create_dataset_request.py:80
msgid "You have Upload File request in progress."
msgstr "لديك طلب رفع ملف قيد التنفيذ."

#: workspaces/tests/test_schema/test_mutations/test_create_log_field_mapping.py:98
#: workspaces/tests/test_schema/test_mutations/test_update_json_schema.py:82
#, python-format
msgid "Dataset with %(request_id)s not found"
msgstr "مجموعة البيانات التي بالمعرف %(request_id)s غير موجودة"

#: workspaces/tests/test_schema/test_mutations/test_create_log_field_mapping.py:215
msgid "Invalid geometry columns ['geom']"
msgstr "أعمدة مجموعة الاحداثيات غير صالحة ['geom']"

#: workspaces/validators/request.py:74 workspaces/validators/request.py:127
#, python-format
msgid "%(error)s"
msgstr "%(error)s"

#: workspaces/validators/workspace.py:49
msgid "Layers sorted ids must be a subset of workspace layers"
msgstr "معرفات الطبقات المرتبة يجب أن تكون مجموعة فرعية من طبقات مساحة العمل"

#: workspaces/validators/workspace.py:97
#, python-format
msgid "Dataset with %(dataset_request_id)s not found"
msgstr "مجموعة البيانات بالمعرف %(dataset_request_id)s غير موجودة"
