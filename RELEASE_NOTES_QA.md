# GeoCore Backend - Release Notes for QA Testing

**Version:** 0.1  
**Date:** August 13, 2025  
**Target Audience:** Quality Assurance Team

---

## 📋 Table of Contents

1. [System Overview](#system-overview)
2. [Architecture & Technology Stack](#architecture--technology-stack)
3. [Authentication & Authorization](#authentication--authorization)
4. [GraphQL API & Endpoints](#graphql-api--endpoints)
5. [Database Models & Relationships](#database-models--relationships)
6. [GIS & Geospatial Features](#gis--geospatial-features)
7. [Filtering & Query System](#filtering--query-system)
8. [AI Chat Functionality](#ai-chat-functionality)
9. [Setup & Configuration](#setup--configuration)
10. [Testing Considerations](#testing-considerations)
11. [Known Limitations](#known-limitations)
12. [Recent Changes](#recent-changes)

---

## 🌟 System Overview

### Purpose

GeoCore Backend is a comprehensive Django-based geospatial platform that provides advanced GIS capabilities, AI-powered
chat functionality, and robust data management for geographic information systems. The system is designed to support
multi-tenant organizations with collaborative workspace management and sophisticated spatial data processing.

### Core Capabilities

- **Multi-tenant Architecture**: Organization-based isolation with role-based access control
- **Geospatial Data Management**: Layer and record management with PostGIS integration
- **AI-Powered Analytics**: OpenAI GPT-4 integration for intelligent geospatial queries
- **Real-time Collaboration**: WebSocket support for team-based GIS projects
- **Advanced Filtering**: Two-level filtering system with AND/OR logic and negation
- **File Processing**: Support for multiple geospatial formats (CSV, GeoJSON, KML, Shapefile, GPKG)
- **GeoServer Integration**: Automated layer publishing and styling
- **Internationalization**: Multi-language support (Arabic/English)

### Key Business Domains

1. **Organizations**: Multi-tenant container for users and resources
2. **Workspaces**: Collaborative environments for GIS projects
3. **Layers**: Geospatial data containers with schema definitions
4. **Records**: Individual spatial features with properties and geometry
5. **Users**: Authentication and role-based access management
6. **Chat AI**: Intelligent query interface for data analysis

---

## 🏗️ Architecture & Technology Stack

### Backend Framework

- **Django 3.2**: Core web framework with Django REST Framework
- **Python 3.10**: Primary programming language
- **Gabbro Framework**: Custom framework extension for enhanced functionality

### Database & Spatial

- **PostgreSQL**: Primary database with ACID compliance
- **PostGIS**: Spatial database extension for geographic objects
- **Django ORM**: Object-relational mapping with spatial field support

### API & Communication

- **GraphQL**: Primary API interface using Graphene-Django

### GIS & Spatial Processing

- **GeoServer**: Map server for publishing spatial data services
- **GeoPandas**: Spatial data analysis and manipulation
- **GDAL/OGR**: Geospatial data abstraction library

### AI & Machine Learning

- **OpenAI GPT-4**: Natural language processing for chat functionality
- **TikToken**: Token counting and management for AI interactions

### Development & Deployment

- **Docker & Docker Compose**: Containerization and orchestration
- **pytest**: Testing framework with coverage reporting
- **Black, flake8**: Code formatting and linting
- **pre-commit**: Git hooks for code quality

### File Storage & Processing

- **Django Storage**: Configurable storage backends (local/cloud)
- **Multiple Format Support**: CSV, GeoJSON, KML, Shapefile, GeoPackage
- **Pandas**: Data manipulation and analysis

---

## 🔐 Authentication & Authorization

### Authentication Mechanisms

- **Token-based Authentication**: Hydra token authentication system
- **External Identity Integration**: Support for external user management systems
- **Session Management**: Django session framework for web interface

### User Status Management

Users have the following status options:

- `ACTIVE`: Full system access
- `INACTIVE`: Limited or no access
- `WAITING`: Pending activation
- `DELETED`: Soft deletion state

### Role-Based Access Control (RBAC)

#### Organization Roles

1. **Organization Owner** (`main-owner`)
    - Full administrative access
    - User management capabilities
    - Organization settings control
    - All workspace and layer permissions

2. **Organization Managers**
    - User management (add, change, delete)
    - Layer and record management
    - Workspace administration
    - Role management (except owner role)

3. **Organization Editors**
    - Layer viewing and record editing
    - Workspace creation and modification
    - Limited user interaction

4. **Organization Viewers**
    - Read-only access to layers and records
    - Workspace viewing permissions

### Permission System

The system uses Django Guardian for object-level permissions with the following permission types:

#### User Permissions

- `view_user`, `add_user`, `change_user`, `delete_user`

#### Workspace Permissions

- `view_workspace`, `add_workspace`, `change_workspace`, `delete_workspace`

#### Role Permissions

- `view_role`, `add_role`, `change_role`, `delete_role`

### Security Considerations for QA Testing

- Test user isolation between organizations
- Verify permission inheritance and delegation
- Test role-based feature access restrictions
- Validate token expiration and refresh mechanisms
- Test unauthorized access attempts and proper error responses

---

## 🔌 GraphQL API & Endpoints

### Main GraphQL Endpoint

- **URL**: `/graphql`
- **Features**: GraphiQL interface enabled for development
- **Authentication**: Token-based authentication required for most operations

### Query Operations

#### User Queries

```graphql
# Get user details
userDetails: UserType

# List users in organization
users(orgId: Int!, pk: Int, workspaceId: Int, excludeIndividuals: Boolean, pageInfo: PageInfo, filters: [DjangoFilterInput]): UserListType

# Get user roles
roles(orgId: Int!, pk: Int, pageInfo: PageInfo, filters: [DjangoFilterInput]): RoleListType

# Get permissions
permissions(orgId: Int!): [PermissionModelType]
workspacePermissions(orgId: Int!): [WorkspacePermissionType]
```

#### Organization Queries

```graphql
# List organizations
organizations(pk: Int, pageInfo: PageInfo, filters: [DjangoFilterInput]): OrganizationListType
```

#### Workspace Queries

```graphql
# List workspaces
workspaces(orgId: Int!, pk: Int, pageInfo: PageInfo, filters: [DjangoFilterInput]): WorkspaceListType

# List datasets
datasets(orgId: Int!, pk: Int, workspaceId: Int!, pageInfo: PageInfo, filters: [DjangoFilterInput]): DatasetListType

# Get workspace requests
workspaceRequests(orgId: Int!, pk: Int, pageInfo: PageInfo, filters: [DjangoFilterInput]): WorkspaceRequestListType

# Preview dataset sample data
datasetSampleData(orgId: Int!, datasetRequestId: Int!): PreviewDataListType
```

#### Layer Queries

```graphql
# List layers
layers(orgId: Int!, workspaceId: Int!, pk: Int, pageInfo: PageInfo, filters: [DjangoFilterInput], filterGroups: [FilterGroupInput]): LayerListType

# List records
records(orgId: Int!, layerId: Int!, pk: Int, pageInfo: PageInfo, filters: [DjangoFilterInput], filterGroups: [FilterGroupInput], autoComplete: Boolean): RecordListType
```

#### Chat AI Queries

```graphql
# Get conversation messages
messages(orgId: Int!, workspaceId: Int!, layersIds: [Int], pageInfo: PageInfo, filters: [DjangoFilterInput]): MessageListType
```

### Mutation Operations

#### User Mutations

```graphql
# Role management
createRole(dataInput: RoleInputType!): CreateRole
updateRole(dataInput: UpdateRoleInputType!): UpdateRole
deleteRole(dataInput: DeleteRoleInputType!): DeleteRole

# User management
addUser(dataInput: AddUserInputType!): AddUser
changeUserRole(dataInput: ChangeUserRoleInputType!): ChangeUserRole
changeUserActiveStatus(dataInput: ChangeUserActiveStatusInputType!): ChangeUserActiveStatus
assignWorkspaceUserPermissions(dataInput: AssignWorkspaceUserPermissionsInputType!): AssignWorkspaceUserPermissions
```

#### Organization Mutations

```graphql
# Organization management
updateOrganization(dataInput: OrganizationInputType!): UpdateOrganization
```

#### Workspace Mutations

```graphql
# Dataset and workspace management
createDataset(dataInput: CreateWorkspaceRequestInputType!): CreateWorkspaceRequest
cancelWorkspaceRequest(dataInput: CancelWorkspaceRequestInputType!): CancelWorkspaceRequest
createLocationFieldMapping(dataInput: CreateLocationFieldMappingInputType!): CreateLocationFieldMapping
updateWorkspace(dataInput: UpdateWorkSpaceInputType!): UpdateWorkSpace
updateJsonSchemas(dataInput: UpdateJSONSchemasInputType!): UpdateJSONSchemas
createWorkspaceLayer(dataInput: CreateWorkSpaceLayerInputType!): CreateWorkSpaceLayer
deleteWorkspace(dataInput: DeleteWorkSpaceInputType!): DeleteWorkSpace
signedUrl(dataInput: SignedURLInputType!): SignedURL
createDesignLayerRequest(dataInput: CreateDesignLayerRequestInputType!): CreateDesignLayerRequest
designLayerJsonSchema(dataInput: DesignLayerJsonSchemaInputType!): DesignLayerJsonSchema
updateDesignLayerRequest(dataInput: UpdateDesignLayerRequestInputType!): UpdateDesignLayerRequest
createEmptyWorkspace(dataInput: CreateEmptyWorkspaceInputType!): CreateEmptyWorkspace
updateWorkspaceLastVisitedDate(dataInput: UpdateWorkSpaceLastVisitedDateInputType!): UpdateWorkSpaceLastVisitedDate
```

#### Layer Mutations

```graphql
# Layer management operations
createLayerFromDataset(dataInput: CreateLayerFromDatasetInputType!): CreateLayerFromDataset
updateLayer(dataInput: UpdateLayerInputType!): UpdateLayer
deleteLayer(dataInput: DeleteLayerInputType!): DeleteLayer
duplicateLayer(dataInput: DuplicateLayerInputType!): DuplicateLayer

# Record management operations  
createRecord(dataInput: CreateRecordInputType!): CreateRecord
updateRecord(dataInput: UpdateRecordInputType!): UpdateRecord
deleteRecord(dataInput: DeleteRecordInputType!): DeleteRecord
bulkCreateRecords(dataInput: BulkCreateRecordsInputType!): BulkCreateRecords
bulkUpdateRecords(dataInput: BulkUpdateRecordsInputType!): BulkUpdateRecords
bulkDeleteRecords(dataInput: BulkDeleteRecordsInputType!): BulkDeleteRecords
```

#### Chat AI Mutations

```graphql
# AI chat operations
sendMessageChatAi(inputForm: NewChatInputType!): ChatAI
resetChatAi(dataInput: ResetNewChatInputType!): ResetChatAI
```

### REST API Endpoints

- `/admin/` - Django Admin interface
- `/proxy/` - GeoServer proxy endpoints

### API Testing Considerations

- Test authentication requirements for each endpoint
- Verify organization-level data isolation
- Test pagination and filtering functionality
- Validate input validation and error handling
- Test GraphQL query complexity and performance
- Verify proper error responses and status codes

---

## 🗄️ Database Models & Relationships

### Core Entity Relationships

```
Organization (1) ←→ (N) User
Organization (1) ←→ (N) Workspace
Workspace (1) ←→ (N) Layer
Dataset (1) ←→ (N) Layer
Layer (1) ←→ (N) Record
Layer (N) ←→ (N) SLD (Styled Layer Descriptor)
User (1) ←→ (N) Workspace (as owner)
Conversation (N) ←→ (N) Layer
Conversation (1) ←→ (N) Message
```

### User Model

**Table**: `users_user`

**Key Fields**:

- `id`: Primary key (BigAutoField)
- `email`: Unique email address (EmailField)
- `phone`: Phone number (CharField)
- `first_name`, `last_name`: User names (CharField)
- `external_key`: External system identifier (CharField)
- `active_status`: User status (TextChoices)
    - `ACTIVE`: Full access
    - `INACTIVE`: Limited access
    - `WAITING`: Pending activation
    - `DELETED`: Soft deleted
- `is_staff`, `is_superuser`: Django admin flags (BooleanField)
- `avatar`: Profile image URL (URLField)

**Relationships**:

- Many-to-Many with Organizations (through ACL)
- One-to-Many with Workspaces (as owner)
- Many-to-Many with Roles (through ACL)

### Organization Model

**Table**: `organizations_organization`

**Key Fields**:

- `id`: Primary key (BigAutoField)
- `external_id`: External system identifier (CharField)
- `settings`: Configuration data (JSONField)
- `created`, `modified`: Timestamps (DateTimeField)

**Relationships**:

- Many-to-Many with Users (ACL individuals)
- Many-to-Many with Roles
- One-to-Many with Workspaces

### Workspace Model

**Table**: `workspaces_workspace`

**Key Fields**:

- `id`: Primary key (BigAutoField)
- `name`: Workspace name (CharField, max_length=255)
- `description`: Workspace description (TextField)
- `thumbnail`: Map thumbnail URL (URLField)
- `last_visited`: Last access timestamp (DateTimeField)
- `layers_sorted_ids`: Ordered layer IDs (JSONField)
- `layers_data`: Layer statistics (JSONField)
    - `layers_count`: Number of layers
    - `records_count`: Total records across layers
- `workspace_type`: Type of workspace (CharField)
    - `UPLOAD_FILE`: File-based workspace
    - `DESIGN_LAYER`: Custom designed workspace
- `owner`: Workspace owner (ForeignKey to User)
- `organization`: Parent organization (ForeignKey to Organization)

**Relationships**:

- Many-to-One with Organization
- Many-to-One with User (owner)
- One-to-Many with Layers
- One-to-Many with Datasets

### Dataset Model

**Table**: `workspaces_dataset`

**Key Fields**:

- `id`: Primary key (BigAutoField)
- `file`: Dataset file URL (URLField)
- `meta_data`: Dataset metadata (JSONField)
- `workspace`: Parent workspace (ForeignKey to Workspace)

**Supported File Formats**:

- CSV, GeoJSON, KML, Shapefile (.shp), GeoPackage (.gpkg)

**Relationships**:

- Many-to-One with Workspace
- One-to-Many with Layers
- One-to-Many with EDAReports

### Layer Model

**Table**: `layers_layer`

**Key Fields**:

- `id`: Primary key (BigAutoField)
- `key`: Unique slug identifier (SlugField, max_length=200)
- `title`: Layer display name (CharField, max_length=200)
- `description`: Layer description (TextField)
- `read_only`: Edit permission flag (BooleanField)
- `status`: Publication status (CharField)
    - `PUBLISHED`: Available in GeoServer
    - `UNPUBLISHED`: Draft state
- `boundaries`: Spatial extent (GeometryField)
- `location_field_mapping`: Coordinate field mapping (JSONField)
    - `coordinate_type`: Type of coordinates
    - `lat_lon_column`: Combined lat/lon column
    - `longitude_column`: Longitude field name
    - `latitude_column`: Latitude field name
- `json_schema`: Form schema definition (JSONField)
- `web_ui_json_schema`: UI schema definition (JSONField)
- `data`: Layer metadata (JSONField)
    - `columns`: List of data columns
    - `summary_fields`: Statistical information
- `records_last_modified`: Last record update (DateTimeField)
- `filters`: Layer-specific filters (JSONField)
- `dataset`: Source dataset (ForeignKey to Dataset)
- `workspace`: Parent workspace (ForeignKey to Workspace)

**Relationships**:

- Many-to-One with Workspace
- Many-to-One with Dataset (nullable)
- One-to-Many with Records
- Many-to-Many with SLDs (styling)
- Many-to-Many with Conversations

### Record Model

**Table**: `layers_record`

**Key Fields**:

- `id`: Primary key (BigAutoField)
- `geometry`: Spatial geometry (GeometryField)
    - Supports Point, LineString, Polygon, MultiPolygon
- `source_properties`: Original data properties (JSONField)
- `map_data`: Processed map data (JSONField)
- `data`: Record-specific data (JSONField)
- `layer`: Parent layer (ForeignKey to Layer)

**Spatial Data Types**:

- Point: Single coordinate location
- LineString: Connected line segments
- Polygon: Closed area with boundaries
- MultiPolygon: Multiple polygon areas

**Relationships**:

- Many-to-One with Layer

### SLD (Styled Layer Descriptor) Model

**Table**: `layers_sld`

**Key Fields**:

- `id`: Primary key (BigAutoField)
- `title`: Style name (CharField)
- `sld_type`: Style type (CharField)
    - `POINTS`: Point styling
    - `HEATMAP`: Heatmap visualization
- `feature_style`: Style configuration (JSONField)
    - Color, size, opacity settings
    - Symbol and pattern definitions

**Relationships**:

- Many-to-Many with Layers

### Conversation Model (Chat AI)

**Table**: `chat_ai_conversation`

**Key Fields**:

- `id`: Primary key (BigAutoField)
- `user`: Conversation owner (ForeignKey to User)
- `workspace`: Context workspace (ForeignKey to Workspace)

**Relationships**:

- Many-to-One with User
- Many-to-One with Workspace
- Many-to-Many with Layers
- One-to-Many with Messages

### Message Model (Chat AI)

**Table**: `chat_ai_message`

**Key Fields**:

- `id`: Primary key (BigAutoField)
- `message`: Message data (JSONField)
    - `user_input`: User question
    - `sql_query`: Generated SQL
    - `sql_result`: Query results
    - `final_result`: AI response
- `conversation`: Parent conversation (ForeignKey to Conversation)

**Relationships**:

- Many-to-One with Conversation

### Database Testing Considerations

- **Data Integrity**: Test foreign key constraints and cascading deletes
- **Spatial Data**: Validate geometry field operations and spatial queries
- **JSON Schema Validation**: Test JSON field structure and validation
- **Performance**: Test query performance with large datasets
- **Migrations**: Verify database migration scripts work correctly
- **Constraints**: Test unique constraints and validation rules
- **Indexing**: Verify spatial and regular indexes are functioning
- **Transactions**: Test atomic operations and rollback scenarios

---

## 🗺️ GIS & Geospatial Features

### Layer Management System

#### Layer Creation Process

1. **File Upload**: Users upload geospatial files through workspace requests
2. **Data Processing**: System processes and validates spatial data
3. **Schema Generation**: Automatic JSON schema creation from data structure
4. **Layer Publishing**: Integration with GeoServer for map service publication
5. **Record Creation**: Individual spatial features stored as records

#### Layer Properties

- **Unique Key**: Slug-based identifier for GeoServer integration
- **Spatial Boundaries**: Automatically calculated extent from records
- **JSON Schema**: Dynamic form definition based on data properties
- **Styling (SLD)**: Configurable visual representation
- **Status Management**: Published/Unpublished states
- **Read-only Mode**: Configurable edit permissions

#### Layer Operations

- **Create from Dataset**: Generate layer from uploaded file
- **Duplicate Layer**: Copy existing layer with all records
- **Update Metadata**: Modify title, description, schema
- **Delete Layer**: Remove layer and associated records
- **Publish/Unpublish**: Control GeoServer availability

### Record Management System

#### Record Structure

- **Geometry**: Primary spatial component (Point, LineString, Polygon, MultiPolygon)
- **Buffer Geometry**: Calculated buffer zones around primary geometry
- **Source Properties**: Original data attributes from uploaded file
- **Map Data**: Processed data optimized for map display
- **Custom Data**: User-defined additional properties

#### Spatial Data Types Support

1. **Point**: Single coordinate locations (lat/lon)
2. **LineString**: Connected sequences of coordinates
3. **Polygon**: Closed areas with interior and exterior boundaries
4. **MultiPolygon**: Complex areas with multiple polygon components

#### Record Operations

- **Create**: Add new spatial features to layers
- **Update**: Modify geometry and properties
- **Delete**: Remove individual records
- **Bulk Operations**: Mass create, update, or delete records
- **Spatial Queries**: Filter records by spatial relationships

### File Upload & Processing

#### Supported Formats

1. **CSV**: Tabular data with coordinate columns
    - Coordinate Types: Separate lat/lon columns or combined column
    - Automatic coordinate detection and validation

2. **GeoJSON**: Standard JSON format for geographic features
    - Feature collections with geometry and properties
    - Coordinate reference system support

3. **KML**: Keyhole Markup Language for Google Earth
    - Placemark extraction with extended data
    - Nested folder structure support

4. **Shapefile (.shp)**: ESRI standard format
    - Complete shapefile package (shp, shx, dbf, prj)
    - Attribute table integration

5. **GeoPackage (.gpkg)**: OGC standard SQLite-based format
    - Multiple layer support
    - Spatial indexing capabilities

#### File Processing Pipeline

1. **Upload Validation**: File format and size verification
2. **Data Extraction**: Parse spatial and attribute data
3. **Coordinate Transformation**: Convert to standard CRS (EPSG:4326)
4. **Schema Detection**: Analyze data structure for JSON schema
5. **Quality Validation**: Check for invalid geometries and data
6. **Record Creation**: Store processed features as database records

#### Location Field Mapping

- **Coordinate Type Detection**: Automatic identification of spatial columns
- **Flexible Mapping**: Support for various coordinate formats
- **Validation**: Coordinate range and format verification
- **Transformation**: Convert between coordinate systems

### GeoServer Integration

#### Automated Publishing

- **Feature Type Creation**: Automatic layer registration in GeoServer
- **CQL Filtering**: Layer-specific data filtering (layer_id = X)
- **Attribute Mapping**: Database field to GeoServer attribute mapping
- **Metadata Assignment**: Layer title, description, and spatial extent

#### Styling System (SLD)

- **Default Styles**: Automatic style generation with random colors
- **Style Types**: Points, heatmaps, and custom visualizations
- **Dynamic Styling**: Runtime style application
- **Style Management**: Create, update, and delete style definitions

### Spatial Data Processing

#### Geometry Operations

- **Boundary Calculation**: Automatic spatial extent computation using PostGIS
- **Buffer Generation**: Create buffer zones around geometries
- **Spatial Validation**: Check for valid geometry structures
- **Coordinate Transformation**: Convert between spatial reference systems

#### PostGIS Integration

- **Spatial Indexing**: Automatic spatial index creation for performance
- **Spatial Functions**: Leverage PostGIS spatial analysis capabilities
- **Geometry Types**: Support for all PostGIS geometry types
- **Spatial Queries**: Complex spatial relationship queries

#### Performance Optimizations

- **Batch Processing**: Bulk record operations for large datasets
- **Background Tasks**: Asynchronous processing using threading
- **Spatial Indexing**: Optimized spatial queries
- **Data Chunking**: Process large files in manageable chunks

### Workspace Integration

#### Collaborative Features

- **Multi-user Access**: Shared workspace environments
- **Layer Organization**: Sorted layer management within workspaces
- **Permission Control**: Workspace-level access management
- **Activity Tracking**: Last visited timestamps and usage statistics

#### Data Management

- **Dataset Linking**: Connect layers to source datasets
- **Metadata Preservation**: Maintain original file information
- **Version Control**: Track layer modifications and updates
- **Backup Integration**: Support for data export and backup

### GIS Testing Considerations

#### Spatial Data Validation

- **Geometry Validation**: Test invalid geometry handling
- **Coordinate System Testing**: Verify CRS transformations
- **Spatial Query Performance**: Test with large spatial datasets
- **Boundary Calculations**: Validate spatial extent computations

#### File Processing Testing

- **Format Support**: Test all supported file formats
- **Large File Handling**: Test performance with large datasets
- **Corrupted File Handling**: Verify error handling for invalid files
- **Coordinate Detection**: Test automatic coordinate field detection

#### GeoServer Integration Testing

- **Publishing Workflow**: Test layer publication process
- **Style Application**: Verify SLD style rendering
- **Service Availability**: Test WMS/WFS service endpoints
- **Performance**: Test map rendering performance

#### Multi-user Scenarios

- **Concurrent Editing**: Test simultaneous record modifications
- **Permission Enforcement**: Verify spatial data access controls
- **Workspace Isolation**: Test data isolation between workspaces
- **Collaboration Features**: Test shared workspace functionality

---

## 🔍 Filtering & Query System

### Overview

The system provides two filtering approaches: a legacy flat filtering system and an advanced two-level filtering system
with group-based logic.

### Legacy Filtering System (DjangoFilterInput)

#### Basic Filter Structure

```graphql
input DjangoFilterInput {
  field: String!           # Database field name
  value: String!           # Filter value
  clause: DjangoFilterChoices  # Filter operation
  is_not: Boolean         # Negation flag
}
```

#### Supported Filter Clauses

**Text Operations**:

- `exact`: Exact match
- `iexact`: Case-insensitive exact match
- `contains`: Contains substring
- `icontains`: Case-insensitive contains

**Numeric Operations**:

- `gt`: Greater than
- `lt`: Less than
- `gte`: Greater than or equal
- `lte`: Less than or equal
- `range`: Value within range
- `number`: Exact numeric match

**List Operations**:

- `in`: Value in list

**Date/Time Operations**:

- `date__gt`, `date__lt`, `date__gte`, `date__lte`: Date comparisons
- `time__gt`, `time__lt`, `time__gte`, `time__lte`: Time comparisons

**Null/Empty Operations**:

- `isnull`: Check for null values
- `isempty`: Check for empty values

#### Legacy Filter Logic

- All filters combined with AND logic
- Individual filters support negation via `is_not`
- Simple flat structure without grouping

### Advanced Filtering System (FilterGroupInput)

#### Two-Level Structure

1. **Top Level**: Filter groups combined with AND logic
2. **Group Level**: Filters within groups combined by group type (AND/OR)

#### Filter Group Structure

```graphql
input FilterGroupInput {
  group_type: GroupTypeChoices!  # AND or OR
  is_not: Boolean               # Group negation
  filters: [FieldFilterInput!]! # Individual filters
}

input FieldFilterInput {
  op: DjangoFilterChoices!      # Filter operation
  field: String!                # Database field name
  value: String!                # Filter value
  is_not: Boolean              # Field negation
}
```

#### Group Types

- `AND`: All filters in group must match
- `OR`: Any filter in group must match

#### Negation Support

- **Group Level**: `is_not` negates entire group result
- **Field Level**: `is_not` negates individual filter result

### Pagination & Ordering

#### PageInfo Structure

```graphql
input PageInfo {
  limit: Int      # Maximum records to return (default: 100,000)
  offset: Int     # Number of records to skip (default: 0)
  order_by: String # Field name for sorting (supports dot notation)
}
```

#### Ordering Features

- **Field Validation**: Checks if order field exists
- **Null Handling**: `nulls_last=True` for descending, `nulls_first=False` for ascending
- **Dot Notation**: Support for nested field ordering (converted to `__`)
- **Descending Order**: Prefix field name with `-`

### Query Performance Optimization

#### filter_qs_paginate_with_count()

- **Dual Query Approach**: Separate queries for data and count
- **Optimized Ordering**: Uses Django F() expressions for performance
- **Error Handling**: Validates field names and provides meaningful errors
- **Memory Efficiency**: Limits result sets to prevent memory issues

### Auto-Complete Support

#### Record Auto-Complete

- **Distinct Values**: Returns unique values for specified fields
- **Field Extraction**: Automatically extracts fields from filter groups
- **Performance Optimization**: Uses `distinct(*fields)` for efficiency
- **Integration**: Works with advanced filtering system

### Filtering in Different Contexts

#### Layer Queries

```graphql
layers(
  orgId: Int!
  workspaceId: Int!
  pk: Int
  pageInfo: PageInfo
  filters: [DjangoFilterInput]        # Legacy system
  filterGroups: [FilterGroupInput]    # Advanced system
)
```

#### Record Queries

```graphql
records(
  orgId: Int!
  layerId: Int!
  pk: Int
  pageInfo: PageInfo
  filters: [DjangoFilterInput]        # Legacy system
  filterGroups: [FilterGroupInput]    # Advanced system
  autoComplete: Boolean              # Enable auto-complete mode
)
```

#### Chat AI Message Queries

```graphql
messages(
  orgId: Int!
  workspaceId: Int!
  layersIds: [Int]
  pageInfo: PageInfo
  filters: [DjangoFilterInput]        # Legacy system only
)
```

### Example Filter Scenarios

#### Simple Text Search (Legacy)

```json
{
  "filters": [
    {
      "field": "title",
      "value": "Road",
      "clause": "icontains"
    }
  ]
}
```

#### Complex Multi-Condition (Advanced)

```json
{
  "filterGroups": [
    {
      "group_type": "AND",
      "filters": [
        {
          "op": "icontains",
          "field": "title",
          "value": "Road"
        },
        {
          "op": "gte",
          "field": "created",
          "value": "2025-01-01"
        }
      ]
    },
    {
      "group_type": "OR",
      "filters": [
        {
          "op": "exact",
          "field": "status",
          "value": "published"
        },
        {
          "op": "exact",
          "field": "read_only",
          "value": "false"
        }
      ]
    }
  ]
}
```

#### Negated Group Example

```json
{
  "filterGroups": [
    {
      "group_type": "OR",
      "is_not": true,
      "filters": [
        {
          "op": "exact",
          "field": "status",
          "value": "deleted"
        },
        {
          "op": "exact",
          "field": "status",
          "value": "archived"
        }
      ]
    }
  ]
}
```

### Filtering Testing Considerations

#### Basic Functionality

- **Filter Clause Testing**: Verify all supported filter operations
- **Data Type Handling**: Test with different data types (string, number, date)
- **Null Value Handling**: Test `isnull` and `isempty` operations
- **Case Sensitivity**: Verify case-sensitive vs case-insensitive operations

#### Advanced Filtering

- **Group Logic**: Test AND vs OR group combinations
- **Negation Testing**: Verify group and field level negation
- **Complex Scenarios**: Test multiple groups with mixed logic
- **Performance**: Test with large datasets and complex filter combinations

#### Edge Cases

- **Empty Filters**: Test behavior with empty filter arrays
- **Invalid Fields**: Test error handling for non-existent fields
- **Invalid Values**: Test validation of filter values
- **Malformed Input**: Test GraphQL input validation

#### Performance Testing

- **Large Datasets**: Test filtering performance with millions of records
- **Complex Queries**: Test deeply nested filter combinations
- **Index Usage**: Verify database indexes are utilized effectively
- **Memory Usage**: Monitor memory consumption with large result sets

#### Integration Testing

- **Pagination**: Test filtering with pagination and ordering
- **Auto-Complete**: Test distinct value extraction with filters
- **Cross-Model**: Test filtering across related models
- **Permission Integration**: Verify filters respect access controls

---

## 🤖 AI Chat Functionality

### Overview

The AI Chat system integrates OpenAI GPT-4 to provide intelligent querying capabilities for geospatial data. Users can
ask natural language questions about their data and receive both human-readable responses and structured data results.

### OpenAI Integration

#### Models Configuration

- **PostgreSQL Model**: `settings.POSTGRES_MODEL` - For data queries
- **GIS Model**: `settings.GIS_MODEL` - For spatial analysis
- **Django Model**: `settings.DJANGO_MODEL` - For general and data action queries

#### API Configuration

- **Client**: OpenAI Python client
- **Temperature**: 0.2 (low randomness for consistent results)
- **Max Tokens**: Configurable via `settings.SQL_GENERATOR_MAX_TOKENS`
- **Timeout**: 10 seconds for API calls

### Question Types

#### 1. DATA Questions

- **Purpose**: Query and analyze tabular data
- **Model**: PostgreSQL-specialized model
- **Process**: Natural language → SQL → Database execution → Human response
- **Example**: "How many records are in the roads layer?"

#### 2. GIS Questions

- **Purpose**: Spatial analysis and geographic queries
- **Model**: GIS-specialized model
- **Process**: Spatial analysis with geographic context
- **Example**: "What is the total area covered by all polygons?"

#### 3. DATA_ACTION Questions

- **Purpose**: Data manipulation and management operations
- **Model**: Django-specialized model
- **Process**: Django ORM operations and data modifications
- **Example**: "Create a new layer with filtered data"

#### 4. GENERAL Questions

- **Purpose**: General information and help queries
- **Model**: Django-specialized model
- **Process**: Contextual responses about system capabilities
- **Example**: "How do I upload a new dataset?"

### Conversation Management

#### Conversation Model

- **User Association**: Each conversation belongs to a specific user
- **Workspace Context**: Conversations are scoped to workspaces
- **Layer Association**: Multiple layers can be associated with conversations
- **Persistence**: Conversations maintain context across sessions

#### Message Model

- **Structured Storage**: Messages stored as JSON with multiple components
- **Conversation Threading**: Messages linked to parent conversations
- **Timestamp Tracking**: Creation and modification timestamps

### Message Structure

#### Message Data Components

```json
{
  "user_input": "Natural language question",
  "sql_query": "Generated SQL query (for DATA questions)",
  "sql_result": [
    /* Raw database results */
  ],
  "final_result": "Human-readable AI response"
}
```

#### Message Processing Flow

1. **Input Validation**: Validate question and context
2. **Model Selection**: Choose appropriate GPT model based on question type
3. **Context Building**: Include layer schemas and conversation history
4. **Query Generation**: Generate SQL or analysis approach
5. **Execution**: Run queries against database
6. **Response Generation**: Create human-readable response
7. **Storage**: Save complete message data

### SQL Generation System

#### Natural Language to SQL Process

1. **Schema Analysis**: Extract layer schemas and column information
2. **Context Building**: Include table structures and relationships
3. **Prompt Engineering**: Construct specialized prompts for SQL generation
4. **Query Generation**: Use GPT to generate PostgreSQL queries
5. **Query Validation**: Basic validation of generated SQL
6. **Execution**: Run queries with error handling

#### Database Schema Context

The system provides GPT with detailed schema information:

```json
{
  "layers_layer": {
    "description": "the table that stores the layers data",
    "properties": {
      "id": {
        "type": "integer",
        "title": "the primary key"
      },
      "title": {
        "type": "string",
        "title": "layer title"
      },
      "status": {
        "type": "string",
        "enum": [
          "published",
          "unpublished"
        ]
      }
    }
  },
  "layers_record": {
    "description": "the table that stores spatial records",
    "properties": {
      "id": {
        "type": "integer",
        "title": "the primary key"
      },
      "layer_id": {
        "type": "integer",
        "title": "foreign key to layers_layer"
      },
      "geometry": {
        "type": "geometry",
        "title": "spatial geometry"
      },
      "data": {
        "type": "object",
        "title": "record properties"
      }
    }
  }
}
```

#### SQL Query Execution

- **Raw SQL Execution**: Direct database queries using Django connection
- **Result Processing**: Convert query results to JSON-serializable format
- **Error Handling**: Catch and handle SQL execution errors
- **Performance Monitoring**: Track query execution time and complexity

### Token Management

#### Token Counting

- **Library**: TikToken for accurate token counting
- **Model-Specific**: Different encodings for different models
- **Conversation Limits**: Manage conversation length to stay within limits
- **Cost Optimization**: Monitor token usage for cost control

#### Context Management

- **Conversation History**: Include relevant previous messages
- **Schema Information**: Provide necessary database schema context
- **Layer Data**: Include sample data for better query generation
- **Prompt Optimization**: Balance context richness with token limits

### Chat AI GraphQL Operations

#### Send Message Mutation

```graphql
mutation SendChatMessage($input: NewChatInputType!) {
  sendMessageChatAi(inputForm: $input) {
    response      # Human-readable AI response
    dbResult      # Raw database results (JSON)
  }
}

input NewChatInputType {
  orgId: Int!
  workspaceId: Int!
  question: String!
  layersIds: [Int]
  questionType: QuestionTypeEnum!
}
```

#### Reset Conversation Mutation

```graphql
mutation ResetChat($input: ResetNewChatInputType!) {
  resetChatAi(dataInput: $input) {
    success
  }
}

input ResetNewChatInputType {
  orgId: Int!
  workspaceId: Int!
  layersIds: [Int]
}
```

#### Messages Query

```graphql
query GetMessages($orgId: Int!, $workspaceId: Int!) {
  messages(orgId: $orgId, workspaceId: $workspaceId) {
    data {
      id
      message {
        userInput
        sqlResult
        finalResult
      }
    }
    count
  }
}
```

### Error Handling

#### API Error Management

- **Timeout Handling**: 10-second timeout for OpenAI API calls
- **Rate Limiting**: Handle OpenAI rate limit responses
- **Authentication Errors**: Manage API key and authentication issues
- **Model Availability**: Handle model unavailability scenarios

#### SQL Error Management

- **Syntax Errors**: Catch and report SQL syntax issues
- **Permission Errors**: Handle database permission problems
- **Data Errors**: Manage data type and constraint violations
- **Performance Issues**: Handle long-running query timeouts

### Security Considerations

#### Data Access Control

- **Organization Isolation**: Queries limited to user's organization data
- **Workspace Scoping**: Conversations scoped to specific workspaces
- **Layer Permissions**: Respect layer-level access controls
- **SQL Injection Prevention**: Parameterized queries and validation

#### AI Safety

- **Query Validation**: Basic validation of generated SQL
- **Result Filtering**: Filter sensitive information from responses
- **Audit Logging**: Log all AI interactions for review
- **Rate Limiting**: Prevent abuse of AI services

### Performance Optimization

#### Caching Strategies

- **Schema Caching**: Cache layer schemas to reduce database queries
- **Conversation Context**: Efficient conversation history management
- **Result Caching**: Cache common query results
- **Token Optimization**: Minimize token usage while maintaining quality

#### Background Processing

- **Async Operations**: Use threading for non-blocking operations
- **Queue Management**: Consider Celery for heavy AI processing
- **Resource Management**: Monitor memory and CPU usage
- **Scaling Considerations**: Plan for multiple concurrent conversations

### AI Chat Testing Considerations

#### Functionality Testing

- **Question Type Routing**: Verify correct model selection for each question type
- **SQL Generation**: Test natural language to SQL conversion accuracy
- **Response Quality**: Evaluate AI response relevance and accuracy
- **Conversation Context**: Test conversation memory and context retention

#### Integration Testing

- **Database Integration**: Test SQL execution against real data
- **Permission Integration**: Verify access control enforcement
- **Workspace Isolation**: Test data isolation between workspaces
- **Error Propagation**: Test error handling throughout the pipeline

#### Performance Testing

- **Response Time**: Measure end-to-end response times
- **Concurrent Users**: Test multiple simultaneous conversations
- **Large Datasets**: Test performance with large data volumes
- **Token Limits**: Test behavior near token limits

#### Security Testing

- **Data Isolation**: Verify users can only access their data
- **SQL Injection**: Test for SQL injection vulnerabilities
- **Prompt Injection**: Test for AI prompt injection attacks
- **Rate Limiting**: Test API rate limiting and abuse prevention

#### Edge Cases

- **Invalid Questions**: Test handling of nonsensical questions
- **Empty Datasets**: Test behavior with empty or minimal data
- **API Failures**: Test OpenAI API failure scenarios
- **Network Issues**: Test timeout and connectivity problems

---

## ⚙️ Setup & Configuration

### Prerequisites

#### System Requirements

- **Python**: 3.10+ (recommended 3.11)
- **Node.js**: Latest LTS version (for frontend development)
- **Docker**: Latest stable version
- **Docker Compose**: v2.0+
- **Git**: For version control

#### External Services

- **PostgreSQL**: 13+ with PostGIS extension
- **GeoServer**: Compatible version for spatial services
- **OpenAI API**: Valid API key for chat functionality

### Docker Setup (Recommended)

#### Quick Start Commands

```bash
# Clone repository
git clone <repository-url>
cd geocore-backend

# Copy environment configuration
cp .env.example .env
# Edit .env with your settings

# Start services
docker-compose up -d

# Run migrations
docker-compose exec geocore_backend python manage.py migrate

# Create superuser
docker-compose exec geocore_backend python manage.py createsuperuser

# Collect static files
docker-compose exec geocore_backend python manage.py collectstatic
```

### Environment Variables

#### Core Django Settings

```bash
# Django Configuration
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# Database Configuration
DATABASE_URL=postgis://postgres:passw0rd@postgres:5432/geocore_backend
DB_HOST=postgres
DB_PORT=5432
DB_NAME=geocore_backend
DB_USER=postgres
DB_PASSWORD=passw0rd 
```

#### Storage Configuration

```bash
# Storage Backend
STORAGE_TYPE=LOCAL  # or CLOUD
STATIC_URL=/static/
MEDIA_URL=/media/
```

#### GeoServer Configuration

```bash
# GeoServer Settings
GEOSERVER_INTERNAL_URL=http://localhost:8080/geoserver/
GEOSERVER_USERNAME=admin
GEOSERVER_PASSWORD=geoserver
WORKSPACE_NAME=geocore
DATA_STORE_NAME=geocore_datastore
```

#### OpenAI Configuration

```bash
# AI Chat Settings
OPENAI_API_KEY=your-openai-api-key
POSTGRES_MODEL=gpt-4
GIS_MODEL=gpt-4
DJANGO_MODEL=gpt-4
SQL_GENERATOR_MAX_TOKENS=4000
```

#### Performance Settings

```bash
# Data Processing
DATASET_SAMPLE_ROWS_NUMBER=5
RECORDS_BATCH_SIZE=1000

# Internationalization
LANGUAGE_CODE=en-us
TIME_ZONE=UTC
USE_I18N=True
USE_L10N=True
USE_TZ=True
```

### Local Development Setup

#### Python Environment

```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate  # Windows

# Install dependencies
pip install -r requirements_dev.txt

# Install pre-commit hooks
pre-commit install
```

#### Database Setup

```bash
# Install PostgreSQL with PostGIS
# Ubuntu/Debian:
sudo apt-get install postgresql postgresql-contrib postgis

# Create database
sudo -u postgres createdb geocore_backend
sudo -u postgres psql -c "CREATE EXTENSION postgis;" geocore_backend

# Run migrations
python manage.py migrate

# Load initial data (if available)
python manage.py loaddata fixtures/initial_data.json
```

#### Development Server

```bash
# Compile translations
python manage.py compilemessages

# Collect static files
python manage.py collectstatic

# Start development server
python manage.py runserver
```

### Testing Configuration

#### Test Database

```bash
# Test database settings (automatic)
TEST_DATABASE_URL=postgis://postgres:passw0rd@localhost:5432/test_geocore_backend

# Run tests
python manage.py test

# Run with coverage
coverage run manage.py test
coverage report
coverage html
```

### Code Quality Tools

#### Pre-commit Hooks

```bash
# Install pre-commit
pip install pre-commit
pre-commit install

# Run manually
pre-commit run --all-files
```

#### Code Formatting

```bash
# Black formatting
black .

# Flake8 linting
flake8

# Django checks
python manage.py check
python manage.py makemigrations --check --dry-run
```

### Production Deployment Considerations

#### Security Settings

```bash
# Production environment variables
DEBUG=False
SECRET_KEY=generate-strong-secret-key
ALLOWED_HOSTS=your-domain.com,api.your-domain.com

# HTTPS Configuration
SECURE_SSL_REDIRECT=True
SECURE_PROXY_SSL_HEADER=HTTP_X_FORWARDED_PROTO,https
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True
```

### QA Environment Setup

#### Isolated Testing Environment

```bash
# QA-specific environment
DEPLOYMENT=QA
DEBUG=False
LOG_LEVEL=INFO

# Test data configuration
DATASET_SAMPLE_ROWS_NUMBER=10
RECORDS_BATCH_SIZE=500

# Reduced AI token limits for testing
SQL_GENERATOR_MAX_TOKENS=2000
```

---

## 📞 Support & Resources

### Documentation

- **API Documentation**: Available at `/graphql` (GraphiQL interface)
- **Admin Interface**: Available at `/admin/`
- **Code Repository**: Internal Git repository

### Development Team Contacts

- **Backend Team**: For API and database issues
- **DevOps Team**: For deployment and infrastructure issues
- **QA Team**: For testing procedures and bug reporting
- **Product Team**: For feature requirements and specifications

### External Resources

- **Django Documentation**: https://docs.djangoproject.com/
- **PostGIS Documentation**: https://postgis.net/documentation/
- **GraphQL Documentation**: https://graphql.org/learn/
- **OpenAI API Documentation**: https://platform.openai.com/docs/

### Issue Reporting

- **Bug Reports**: Use internal issue tracking system
- **Feature Requests**: Submit through product management channels
- **Security Issues**: Report through secure channels
- **Performance Issues**: Include detailed performance metrics

---

**Document Version**: 1.0
**Last Updated**: August 13, 2025
 
*This document is maintained by the QA team and updated with each major release.*

