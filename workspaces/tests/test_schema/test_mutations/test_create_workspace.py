import json
import os

from django.conf import settings
from django.contrib.auth import get_user_model
from django.test.utils import override_settings
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from gabbro.layers_engine.enums import LayerStatus

from app.settings import BASE_DIR
from common.tests.factories import (
    WorkspaceRequestFactory,
    BaseTestMixin,
    DatasetFactory,
    UserFactory,
)
from workspaces.models import WorkspaceRequestChoices, Workspace, EDAReportSourceChoices

User = get_user_model()


@override_settings(MEDIA_URL="/common/tests/fixtures/")
@override_settings(MEDIA_ROOT=os.path.join(BASE_DIR, "common", "tests", "fixtures"))
class TestCreateWorkSpace(BaseTestMixin):
    def setUp(self):
        super().setUp()
        self.dataset_sample_data = [
            {
                "data": [
                    "39.127902897026",
                    "39.1440719092567",
                    "39.1267282572396",
                    "39.1296885344199",
                    "39.1539532895001",
                ],
                "column": "X",
            },
            {
                "data": [
                    "22.1779350859965",
                    "22.1656821170019",
                    "22.1734079704979",
                    "22.176827142974",
                    "22.184230812",
                ],
                "column": "Y",
            },
            {
                "data": ["1", "70632847", "72462592", "72952076", "74465838"],
                "column": "id",
            },
            {"data": ["3971", "7798", "4331", "5237", "4565"], "column": "parcel_num"},
            {
                "data": ["412?/?", "412?/?", "412?/?", "412?/?", "412?/?"],
                "column": "plan_numbe",
            },
            {"data": ["", "", "", "", ""], "column": "block_numb"},
            {"data": ["????", "????", "????", "????", "????"], "column": "land_use"},
            {"data": ["", "", "", "", ""], "column": "sub_land_u"},
            {"data": ["0", "0", "0", "0", "0"], "column": "commercial"},
            {"data": ["466", "466", "466", "466", "466"], "column": "index_righ"},
            {
                "data": ["FR-3-101", "FR-3-101", "FR-3-101", "FR-3-101", "FR-3-101"],
                "column": "PA_CODE",
            },
        ]

        self.dataset_columns = [
            "X",
            "Y",
            "id",
            "parcel_num",
            "plan_numbe",
            "block_numb",
            "land_use",
            "sub_land_u",
            "commercial",
            "index_righ",
            "PA_CODE",
        ]
        self.dataset = DatasetFactory(
            file=os.path.join(settings.MEDIA_ROOT, "sample_geojson.geojson"),
            meta_data={
                "columns": self.dataset_columns,
                "sample_data": self.dataset_sample_data,
                "eda_reports": [
                    {
                        "status": LayerStatus.PUBLISHED.value,
                        "hash": "123456789",
                        "report_path": "https://test.com/report.pdf",
                        "created_at": timezone.now().isoformat(),
                        "errors": None,
                        "source": EDAReportSourceChoices.YDATA.value,
                    }
                ],
            },
        )

        # Create a workspace request in IN_PROGRESS state
        self.dataset_request = WorkspaceRequestFactory(
            dataset=self.dataset,
            created_by=self.user,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            organization=self.organization,
            layer_data={
                "title": "test",
                "color": "#e3cec7",
                "location_field_mapping": {
                    "coordinate_type": "Point",
                    "lat_lon_column_num": "column",
                    "lang_lat_column": "geometry",
                },
                "json_schema": {
                    "json_schema": {"test": "test"},
                    "web_ui_json_schema": {"test": "test"},
                },
            },
        )
        self.mutation = """
        mutation MyMutation($datasetRequestId: Int!, $mapDataColumns: JSONString!, $orgId: Int!) {
          createWorkspaceLayer(
            dataInput: {datasetRequestId: $datasetRequestId, mapDataColumns: $mapDataColumns, orgId: $orgId}
          ) {
            workspace {
              thumbnail
              since
              name
              lastVisited
              id
              description
              created
            }
          }
        }
        """
        self.variables = {
            "datasetRequestId": self.dataset_request.id,
            "mapDataColumns": json.dumps(self.dataset_columns),
            "orgId": self.organization.id,
        }

    def test_query_authorization(self):
        """Test that unauthorized users cannot create a workspace."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.non_auth_request
        )
        # Check if the query fails with an authorization error
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_invalid_dataset_request_id(self):
        """Test that an invalid dataset request ID is handled properly."""
        # Use a non-existent dataset request ID
        invalid_variables = self.variables.copy()
        invalid_variables["datasetRequestId"] = 99999

        response = self.client.execute(
            self.mutation, variables=invalid_variables, context=self.auth_request
        )
        # Check if the query fails with a dataset request error
        self.assertIn("errors", response)
        self.assertEqual(
            response["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {"datasetRequestId": "Dataset with 99999 not found"},
                    "status": 400,
                    "status_text": "Bad Request",
                }
            },
        )

    def test_cancelled_dataset_request(self):
        """Test that a cancelled dataset request is handled properly."""
        # Cancel the request first
        self.dataset_request.status = WorkspaceRequestChoices.CANCELLED
        self.dataset_request.save()

        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query fails with a dataset request error
        self.assertIn("errors", response)
        self.assertEqual(
            response["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {
                        "datasetRequestId": f"Dataset with {self.dataset_request.id} not found"
                    },
                    "status": 400,
                    "status_text": "Bad Request",
                }
            },
        )

    def test_other_user_request_regular_user(self):
        """Test that a regular user cannot create a workspace from another user's request."""
        # Create a request by another user
        other_user = UserFactory(is_superuser=False)
        other_user_request = WorkspaceRequestFactory(
            dataset=self.dataset,
            created_by=other_user,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            organization=self.organization,
            layer_data={
                "title": "Other User Dataset",
                "color": "#e3cec7",
                "location_field_mapping": {
                    "coordinate_type": "Point",
                    "lat_lon_column_num": "column",
                    "lang_lat_column": "geometry",
                },
                "json_schema": {
                    "json_schema": {"test": "test"},
                    "web_ui_json_schema": {"test": "test"},
                },
            },
        )

        # Try to create a workspace from the other user's request
        other_variables = self.variables.copy()
        other_variables["datasetRequestId"] = other_user_request.id

        response = self.client.execute(
            self.mutation, variables=other_variables, context=self.auth_request
        )
        # Check if the query fails with a permission error
        self.assertIn("errors", response)
        self.assertEqual(
            response["errors"][0]["extensions"],
            {
                "http": {
                    "reason": {
                        "datasetRequestId": f"Dataset with {other_user_request.id} not found"
                    },
                    "status": 400,
                    "status_text": "Bad Request",
                }
            },
        )

    def test_other_user_request_superuser(self):
        """Test that a superuser can create a workspace from another user's request."""
        # Create a request by another user
        other_user = UserFactory(is_superuser=False)
        other_user_request = WorkspaceRequestFactory(
            dataset=self.dataset,
            created_by=other_user,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            organization=self.organization,
            layer_data={
                "title": "Other User Dataset",
                "color": "#e3cec7",
                "location_field_mapping": {
                    "coordinate_type": "Point",
                    "lat_lon_column_num": "column",
                    "lang_lat_column": "geometry",
                },
                "json_schema": {
                    "json_schema": {"test": "test"},
                    "web_ui_json_schema": {"test": "test"},
                },
            },
        )

        # Try to create a workspace from the other user's request as a superuser
        other_variables = self.variables.copy()
        other_variables["datasetRequestId"] = other_user_request.id

        response = self.client.execute(
            self.mutation,
            variables=other_variables,
            context=self.super_user_auth_request,
        )
        # The mutation should execute successfully
        self.assertNotIn("errors", response)

        # Verify the workspace was created
        data = response["data"]["createWorkspaceLayer"]["workspace"]
        self.assertEqual(data["name"], other_user_request.layer_data["title"])
        self.assertTrue(Workspace.objects.filter(id=data["id"]).exists())

        # Verify the dataset request is now finished
        other_user_request.refresh_from_db()
        self.assertEqual(other_user_request.status, WorkspaceRequestChoices.FINISHED)

    def test_successful_creation(self):
        """Test successful creation of a workspace."""
        response = self.client.execute(
            self.mutation, variables=self.variables, context=self.auth_request
        )
        # Check if the query is successful
        self.assertNotIn("errors", response)

        # Verify the response data
        data = response["data"]["createWorkspaceLayer"]["workspace"]
        self.assertEqual(data["name"], self.dataset_request.layer_data["title"])

        # Verify the workspace was created
        workspace = Workspace.objects.get(id=data["id"])
        self.assertEqual(workspace.name, self.dataset_request.layer_data["title"])

        # Verify the dataset request is now finished
        self.dataset_request.refresh_from_db()
        self.assertEqual(self.dataset_request.status, WorkspaceRequestChoices.FINISHED)

        # Verify the layer was created
        self.assertIsNotNone(self.dataset_request.layer)
