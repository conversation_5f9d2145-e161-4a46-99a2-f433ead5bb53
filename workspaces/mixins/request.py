from typing import Optional

from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from gabbro.graphene import BadRequest, Forbidden

from users.models import User
from workspaces.models import (
    RequestTypeChoices,
    WorkspaceRequest,
    WorkspaceRequestChoices,
)


class WorkspaceRequestMixin:
    @staticmethod
    def validate_in_progress_workspace_request(
        user: User, org_id: int, request_type: RequestTypeChoices
    ):
        org_query = Q(
            Q(organization_id=org_id)
            if user.is_superuser
            else Q(organization_id=org_id) & Q(created_by=user),
        )

        in_progress_dataset_request = WorkspaceRequest.objects.filter(
            org_query,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            request_type=request_type,
        ).exists()
        if in_progress_dataset_request:
            raise BadRequest(
                reason={
                    "dataset_file": _("You have %(request_type)s request in progress.")
                    % {"request_type": request_type.label}
                }
            )

    @staticmethod
    def validate_and_get_in_progress_workspace_request(
        user: User,
        org_id: Optional[int],
        workspace_request_id: Optional[int],
        request_type: Optional[RequestTypeChoices] = None,
    ) -> WorkspaceRequest:
        org_query = Q(
            Q(organization_id=org_id)
            if user.is_superuser
            else Q(organization_id=org_id) & Q(created_by=user),
        )
        if request_type:
            org_query &= Q(request_type=request_type)
        workspace_request = (
            WorkspaceRequest.objects.filter(
                org_query,
                id=workspace_request_id,
                status=WorkspaceRequestChoices.IN_PROGRESS,
                layer_data__isnull=False,
            )
            .select_related("dataset")
            .first()
        )
        if not workspace_request:
            raise BadRequest(
                reason={
                    "workspace_request_id": _(
                        "Dataset with %(workspace_request_id)s not found"
                    )
                    % {"workspace_request_id": workspace_request_id}
                }
            )
        return workspace_request

    @staticmethod
    def authorize_workspace_request(user: User, workspace_request: WorkspaceRequest):
        permissions = (workspace_request.created_by == user, user.is_superuser)
        if not any(permissions):
            raise Forbidden(
                reason={
                    "user": _(
                        "Permission denied, you do not have permission to this action."
                    )
                    % {}
                }
            )


def inject_location_field_mapping_into_layer_data(
    dataset_request: WorkspaceRequest, location_field_mapping
):
    if old_location_field_mapping := dataset_request.layer_data.get(
        "location_field_mapping"
    ):
        location_field_mapping = {
            **old_location_field_mapping,
            **location_field_mapping,
        }
    dataset_request.layer_data["location_field_mapping"] = location_field_mapping
    dataset_request.save(update_fields=["layer_data", "modified"])
    dataset_request.dataset.set_metadata(
        {"location_field_mapping": location_field_mapping}
    )
