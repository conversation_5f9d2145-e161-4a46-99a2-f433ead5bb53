from typing import TypedDict, Optional


class LocationFieldMapping(TypedDict):
    coordinate_type: str
    lat_lon_column_num: str
    longitude_column: Optional[str]
    latitude_column: Optional[str]
    lang_lat_column: Optional[str]


class LayerData(TypedDict):
    title: str
    description: str
    color: str
    read_only: bool
    created: str
    location_field_mapping: LocationFieldMapping
