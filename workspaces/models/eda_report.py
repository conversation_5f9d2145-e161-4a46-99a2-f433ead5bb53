from django.db import models
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel


class EDAReportSourceChoices(models.TextChoices):
    YDATA = "ydata", _("YData")
    SWEETVIZ = "sweetviz", _("SweetViz")


class EDAReport(TimeStampedModel):
    # TODO: Make the EDA related with the layer && keep an original copy in the dataset meta data
    dataset = models.ForeignKey(
        "workspaces.Dataset",
        on_delete=models.SET_NULL,
        null=True,
        related_name="eda_reports",
        verbose_name=_("dataset"),
    )
    hash_code = models.CharField(max_length=500, verbose_name=_("hash code"))
    file = models.URLField(verbose_name=_("file"))
    source = models.CharField(
        max_length=20, choices=EDAReportSourceChoices.choices, verbose_name=_("sources")
    )

    class Meta:
        verbose_name = _("EDA Report")
        verbose_name_plural = _("EDA Reports")
