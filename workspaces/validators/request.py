from pathlib import Path
from typing import Dict

from django.utils.translation import gettext_lazy as _
from gabbro.graphene import BadRequest

from common.handlers.dataset_files import validate_shapefile_zip
from common.interfaces import InputValidation
from common.utils import get_json_schema_errors
from layers.serializers.iso_metadata import ISOMetadataSerializer
from organizations.models import Organization
from users.models import User
from workspaces.mixins import WorkspaceRequestMixin, DatasetMixin, WorkspaceMixin
from workspaces.models import WorkspaceRequest, RequestTypeChoices
from workspaces.serializers import WorkspaceRequestSerializer, LayerDataSerializer


class CreateLocationFieldMappingValidation(InputValidation, WorkspaceRequestMixin):
    def validate_and_get_data(
        self, data_input: dict, user: User, organization: Organization, **kwargs
    ) -> Dict[str, WorkspaceRequest]:
        dataset_request = self.validate_and_get_in_progress_workspace_request(
            user=user,
            org_id=data_input.pop("org_id", None),
            workspace_request_id=data_input.pop("dataset_request_id", None),
            request_type=RequestTypeChoices.UPLOAD_FILE,
        )
        return {"dataset_request": dataset_request}


class CreateWorkspaceRequestValidation(
    InputValidation, WorkspaceRequestMixin, DatasetMixin, WorkspaceMixin
):
    def validate_and_get_data(
        self, data_input: dict, user: User, organization: Organization, **kwargs
    ):
        valid_data = dict()
        dataset_file = data_input["dataset_file"]
        self.validate_in_progress_workspace_request(
            user=user,
            org_id=data_input["org_id"],
            request_type=RequestTypeChoices.UPLOAD_FILE,
        )
        self.validate_dataset_file(dataset_file)

        if Path(dataset_file).suffix == ".zip":
            try:
                shp_member = validate_shapefile_zip(dataset_file)
                valid_data["shp_member"] = shp_member
            except Exception as e:
                raise BadRequest(reason={"dataset_file": e.args[0]})

        if workspace_id := data_input.get("workspace_id"):
            self.validate_and_get_workspace(
                workspace_id=workspace_id, org_id=data_input["org_id"]
            )
        return valid_data


class UpdateJSONSchemasValidator(CreateLocationFieldMappingValidation):
    def validate_and_get_data(
        self, data_input: dict, user: User, organization: Organization, **kwargs
    ):
        data = super().validate_and_get_data(data_input, user, organization)
        self.validate_json_schemas(data_input)
        return data

    @staticmethod
    def validate_json_schemas(data_input: dict):
        error = get_json_schema_errors(schema=data_input["json_schema"])
        if error:
            raise BadRequest(reason={"json_schema": _("%(error)s") % {"error": error}})


class CreateDesignLayerRequestValidator(InputValidation, WorkspaceRequestMixin):
    def validate_and_get_data(
        self, data_input: dict, user: User, organization: Organization, **kwargs
    ):
        org_id = data_input["org_id"]
        self.validate_in_progress_workspace_request(
            user=user, org_id=org_id, request_type=RequestTypeChoices.DESIGN_LAYER
        )
        serializer = WorkspaceRequestSerializer(
            data={
                "created_by": user.id,
                "layer_data": data_input,
                "organization": data_input["org_id"],
            },
        )
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        return dict()


class UpdateDesignLayerRequestValidator(InputValidation, WorkspaceRequestMixin):
    def validate_and_get_data(
        self, data_input: dict, user: User, organization: Organization, **kwargs
    ) -> dict:
        workspace_request = self.validate_and_get_in_progress_workspace_request(
            user=user,
            org_id=data_input.pop("org_id", None),
            workspace_request_id=data_input.pop("workspace_request_id", None),
            request_type=RequestTypeChoices.DESIGN_LAYER,
        )
        serializer = LayerDataSerializer(data=data_input)
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        return {"workspace_request": workspace_request}


class DesignLayerJsonSchemaValidator(
    InputValidation, WorkspaceRequestMixin, WorkspaceMixin
):
    def validate_and_get_data(
        self, data_input: dict, user: User, organization: Organization, **kwargs
    ):
        workspace_request = self.validate_and_get_in_progress_workspace_request(
            user=user,
            org_id=data_input.get("org_id"),
            workspace_request_id=data_input.pop("workspace_request_id", None),
            request_type=RequestTypeChoices.DESIGN_LAYER,
        )
        error = get_json_schema_errors(schema=data_input["json_schema"])
        if error:
            raise BadRequest(reason={"json_schema": _("%(error)s") % {"error": error}})
        workspace = None
        if workspace_id := data_input.get("workspace_id"):
            workspace = self.validate_and_get_workspace(
                workspace_id=workspace_id, org_id=data_input["org_id"]
            )
        return dict(workspace_request=workspace_request, workspace=workspace)


class CreateISOMetaDataValidator(InputValidation, WorkspaceRequestMixin):
    def validate_and_get_data(
        self, data_input: dict, user: User, organization: Organization, **kwargs
    ):
        workspace_request = self.validate_and_get_in_progress_workspace_request(
            user=user,
            org_id=data_input.pop("org_id", None),
            workspace_request_id=data_input.pop("workspace_request_id", None),
        )
        serializer = ISOMetadataSerializer(data=data_input)
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        return {"workspace_request": workspace_request}
