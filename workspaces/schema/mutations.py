import os
from urllib.parse import urljoin

import graphene
from django.conf import settings
from django.db.models import Q
from django.utils import timezone
from django.utils.crypto import get_random_string
from graphene_gis.scalars import J<PERSON>NScalar

from common.service import Service
from common.utils import (
    authentication_required,
    organization_required,
    authorize_user,
    authorize_multiple_objects_for_user,
)
from common.utils.graphene.mutations import generate_upload_signed_url_v4
from organizations.perms_constants import CHANGE_WORKSPACE, VIEW_WORKSPACE
from workspaces.mixins import WorkspaceMixin
from workspaces.models import WorkspaceRequest, WorkspaceRequestChoices
from workspaces.permissions import (
    CreateWorkspaceRequestPerms,
    WorkspacePermissions,
    UpdateWorkspaceRequestPerms,
    CreateWorkspacePermissions,
    DesignLayerJsonSchemaPerms,
    DeleteWorkspacePermissions,
    UpdateDesignLayerRequestPerms,
    CreateEmptyWorkspacePerms,
)
from workspaces.schema.input_object_types import (
    CreateDatasetInputType,
    CancelWorkspaceRequestInputType,
    LocationFieldMappingInputType,
    UpdateWorkSpaceInputType,
    UpdateJSONSchemaInputType,
    CreateWorkSpaceInputType,
    DeleteWorkSpaceInputType,
    CreateDesignLayerRequestInputType,
    DesignLayerJsonSchemaInputType,
    UpdateDesignLayerRequestInputType,
    CreateEmptyWorkspaceInputType,
    UpdateWorkSpaceLastVisitedDateInputType,
    ISOMetaDataInputType,
)
from workspaces.schema.object_types import (
    WorkspaceRequestType,
    WorkspaceType,
)
from workspaces.strategies.request import (
    CreateLocationFieldMappingStrategy,
    CreateUploadFileRequestStrategy,
    UpdateJSONSchemasStrategy,
    CreateDesignLayerRequestStrategy,
    DesignLayerJsonSchemaStrategy,
    UpdateDesignLayerRequestStrategy,
    CreateISOMetaDataStrategy,
)
from workspaces.strategies.workspace import (
    UpdateWorkSpaceStrategy,
    CreateWorkSpaceStrategy,
    DeleteWorkspaceStrategy,
    CreateEmptyWorkspaceStrategy,
)
from workspaces.validators import (
    CreateLocationFieldMappingValidation,
    CreateWorkspaceRequestValidation,
    UpdateWorkspaceValidator,
    UpdateJSONSchemasValidator,
    CreateWorkspaceValidator,
    DeleteWorkspaceValidator,
    CreateDesignLayerRequestValidator,
    DesignLayerJsonSchemaValidator,
    UpdateDesignLayerRequestValidator,
    CreateEmptyWorkspaceValidator,
    CreateISOMetaDataValidator,
)

MEDIA_URL = getattr(settings, "MEDIA_URL")


class CreateWorkspaceRequest(graphene.Mutation):
    dataset_request = graphene.Field(WorkspaceRequestType)

    class Input:
        data_input = CreateDatasetInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            strategy=CreateUploadFileRequestStrategy(),
            validator=CreateWorkspaceRequestValidation(),
            perms=CreateWorkspaceRequestPerms(),
        )
        dataset_request = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return CreateWorkspaceRequest(dataset_request=dataset_request)


class CancelWorkspaceRequest(graphene.Mutation):
    closed = graphene.Boolean()

    class Input:
        close_input = CancelWorkspaceRequestInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, close_input):
        user = info.context.user
        organization = info.context.organization
        authorize_user(model_obj=organization, user=user, permission=CHANGE_WORKSPACE)
        org_query = Q(
            Q(organization=organization)
            if user.is_superuser
            else Q(organization=organization) & Q(created_by=user),
        )
        in_progress_dataset_request = WorkspaceRequest.objects.filter(
            org_query,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            id=close_input["dataset_request_id"],
        )
        in_progress_dataset_request.update(status=WorkspaceRequestChoices.CANCELLED)
        return CancelWorkspaceRequest(closed=True)


class CreateLocationFieldMapping(graphene.Mutation):
    dataset_request = graphene.Field(WorkspaceRequestType)

    class Input:
        data_input = LocationFieldMappingInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        request_service = Service(
            strategy=CreateLocationFieldMappingStrategy(),
            validator=CreateLocationFieldMappingValidation(),
            perms=UpdateWorkspaceRequestPerms(),
        )
        dataset_request = request_service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return CreateLocationFieldMapping(dataset_request=dataset_request)


class UpdateJSONSchemas(graphene.Mutation):
    dataset_request = graphene.Field(WorkspaceRequestType)

    class Input:
        data_input = UpdateJSONSchemaInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            strategy=UpdateJSONSchemasStrategy(),
            validator=UpdateJSONSchemasValidator(),
            perms=UpdateWorkspaceRequestPerms(),
        )
        dataset_request = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return UpdateJSONSchemas(dataset_request=dataset_request)


class CreateISOMetaData(graphene.Mutation):
    iso_metadata = graphene.Field(JSONScalar)

    class Input:
        data_input = ISOMetaDataInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            strategy=CreateISOMetaDataStrategy(),
            validator=CreateISOMetaDataValidator(),
            perms=UpdateWorkspaceRequestPerms(),
        )
        iso_metadata = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return CreateISOMetaData(iso_metadata=iso_metadata)


class CreateWorkSpaceLayer(graphene.Mutation):
    workspace = graphene.Field(WorkspaceType)

    class Input:
        data_input = CreateWorkSpaceInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            strategy=CreateWorkSpaceStrategy(),
            validator=CreateWorkspaceValidator(),
            perms=CreateWorkspacePermissions(),
        )
        workspace = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return CreateWorkSpaceLayer(workspace=workspace)


class UpdateWorkSpace(graphene.Mutation):
    workspace = graphene.Field(WorkspaceType)

    class Input:
        data_input = UpdateWorkSpaceInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            strategy=UpdateWorkSpaceStrategy(),
            validator=UpdateWorkspaceValidator(),
            perms=WorkspacePermissions(),
        )
        workspace = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return UpdateWorkSpace(workspace=workspace)


class UpdateWorkSpaceLastVisitedDate(graphene.Mutation):
    workspace = graphene.Field(WorkspaceType)

    class Input:
        data_input = UpdateWorkSpaceLastVisitedDateInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        workspace = WorkspaceMixin.validate_and_get_workspace(
            data_input["workspace_id"], data_input["org_id"]
        )
        authorize_multiple_objects_for_user(
            models_objs=[organization, workspace], perm=VIEW_WORKSPACE, user=user
        )
        workspace.last_visited = timezone.now()
        workspace.save(update_fields=["last_visited"])
        return UpdateWorkSpaceLastVisitedDate(workspace=workspace)


class DeleteWorkSpace(graphene.Mutation):
    deleted = graphene.Boolean()

    class Input:
        data_input = DeleteWorkSpaceInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(self, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            strategy=DeleteWorkspaceStrategy(),
            validator=DeleteWorkspaceValidator(),
            perms=DeleteWorkspacePermissions(),
        )
        service.handle(user=user, organization=organization, data_input=data_input)
        return DeleteWorkSpace(deleted=True)


class SignedURL(graphene.Mutation):
    class Arguments:
        file_extension = graphene.String(required=True)

    signed_url = graphene.String()
    blob_url = graphene.String()

    @staticmethod
    @authentication_required
    def mutate(root, info, file_extension):
        file_name = f"{get_random_string(64)}.{file_extension}"
        blob_name = os.path.join("media", "attachments", "workspaces", file_name)
        blob_url = urljoin(MEDIA_URL, blob_name)
        signed_url = generate_upload_signed_url_v4(blob_name)
        return SignedURL(signed_url=signed_url, blob_url=blob_url)


class CreateDesignLayerRequest(graphene.Mutation):
    workspace_request = graphene.Field(WorkspaceRequestType)

    class Input:
        data_input = CreateDesignLayerRequestInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            perms=CreateWorkspaceRequestPerms(),
            validator=CreateDesignLayerRequestValidator(),
            strategy=CreateDesignLayerRequestStrategy(),
        )
        workspace_request = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return CreateDesignLayerRequest(workspace_request=workspace_request)


class UpdateDesignLayerRequest(graphene.Mutation):
    workspace_request = graphene.Field(WorkspaceRequestType)

    class Input:
        data_input = UpdateDesignLayerRequestInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            perms=UpdateDesignLayerRequestPerms(),
            validator=UpdateDesignLayerRequestValidator(),
            strategy=UpdateDesignLayerRequestStrategy(),
        )
        workspace_request = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return UpdateDesignLayerRequest(workspace_request=workspace_request)


class DesignLayerJsonSchema(graphene.Mutation):
    workspace = graphene.Field(WorkspaceType)

    class Input:
        data_input = DesignLayerJsonSchemaInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            perms=DesignLayerJsonSchemaPerms(),
            validator=DesignLayerJsonSchemaValidator(),
            strategy=DesignLayerJsonSchemaStrategy(),
        )
        workspace = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return DesignLayerJsonSchema(workspace=workspace)


class CreateEmptyWorkspace(graphene.Mutation):
    workspace = graphene.Field(WorkspaceType)

    class Input:
        data_input = CreateEmptyWorkspaceInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            perms=CreateEmptyWorkspacePerms(),
            validator=CreateEmptyWorkspaceValidator(),
            strategy=CreateEmptyWorkspaceStrategy(),
        )
        workspace = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return CreateEmptyWorkspace(workspace=workspace)


class Mutation(graphene.ObjectType):
    create_dataset = CreateWorkspaceRequest.Field()
    cancel_workspace_request = CancelWorkspaceRequest.Field()
    create_location_field_mapping = CreateLocationFieldMapping.Field()
    update_workspace = UpdateWorkSpace.Field()
    update_json_schemas = UpdateJSONSchemas.Field()
    create_workspace_layer = CreateWorkSpaceLayer.Field()
    delete_workspace = DeleteWorkSpace.Field()
    signed_url = SignedURL.Field()
    create_design_layer_request = CreateDesignLayerRequest.Field()
    design_layer_json_schema = DesignLayerJsonSchema.Field()
    update_design_layer_request = UpdateDesignLayerRequest.Field()
    create_empty_workspace = CreateEmptyWorkspace.Field()
    update_workspace_last_visited_date = UpdateWorkSpaceLastVisitedDate.Field()
    create_iso_metadata = CreateISOMetaData.Field()
