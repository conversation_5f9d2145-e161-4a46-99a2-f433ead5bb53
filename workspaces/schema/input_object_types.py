import graphene

from layers.models import GeometryTypes
from workspaces.enums import LongLatEnum


class CreateDatasetInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    title = graphene.String(required=True)
    description = graphene.String()
    color = graphene.String(required=True)
    read_only = graphene.Boolean(default=False)
    dataset_file = graphene.String(required=True)
    workspace_id = graphene.Int(required=False)


class CancelWorkspaceRequestInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    dataset_request_id = graphene.Int(required=True)


class LocationFieldMappingInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    dataset_request_id = graphene.Int(required=True)
    coordinate_type = graphene.Field(
        graphene.Enum.from_enum(GeometryTypes), required=True
    )
    lat_lon_column_num = graphene.Field(
        graphene.Enum.from_enum(LongLatEnum), required=True
    )
    longitude_column = graphene.String(
        required=False, allow_null=True, allow_blank=True
    )
    latitude_column = graphene.String(required=False, allow_null=True, allow_blank=True)
    lang_lat_column = graphene.String(required=False, allow_null=True, allow_blank=True)


class UpdateWorkSpaceInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    workspace_id = graphene.Int(required=True)
    layers_sorted_ids = graphene.List(graphene.Int)
    update_visited_date = graphene.Boolean(default=False)
    name = graphene.String()
    description = graphene.String()
    thumbnail = graphene.String()


class UpdateJSONSchemaInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    dataset_request_id = graphene.Int(required=True)
    form_data = graphene.JSONString(required=True)
    json_schema = graphene.JSONString(required=True)
    web_ui_json_schema = graphene.JSONString()


class CreateWorkSpaceInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    dataset_request_id = graphene.Int(required=True)
    map_data_columns = graphene.JSONString()


class DeleteWorkSpaceInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    workspace_id = graphene.Int(required=True)


class CreateDesignLayerRequestInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    title = graphene.String(required=True)
    description = graphene.String()
    color = graphene.String(required=True)
    read_only = graphene.Boolean(default=False)


class UpdateDesignLayerRequestInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    workspace_request_id = graphene.Int(required=True)
    title = graphene.String(required=True)
    description = graphene.String()
    color = graphene.String(required=True)
    read_only = graphene.Boolean(default=False)


class DesignLayerJsonSchemaInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    workspace_request_id = graphene.Int(required=True)
    json_schema = graphene.JSONString(required=True)
    summary_fields = graphene.JSONString(required=True)
    workspace_id = graphene.Int(required=False)


class CreateEmptyWorkspaceInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    name = graphene.String(required=True)
    description = graphene.String()


class UpdateWorkSpaceLastVisitedDateInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    workspace_id = graphene.Int(required=True)


class ISOMetaDataInputType(graphene.InputObjectType):
    org_id = graphene.Int(required=True)
    workspace_request_id = graphene.Int(required=True)
    title = graphene.String(required=True)
    description = graphene.String(required=True)
    dataset_creation_date = graphene.String(required=True)
    tags = graphene.List(graphene.String, required=True)
    responsible_authority_name = graphene.String(required=True)
    responsible_authority_role = graphene.String(required=True)
    responsible_authority_phone = graphene.String(required=True)
    responsible_authority_email = graphene.String(required=True)
