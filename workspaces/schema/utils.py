from django.conf import settings

from django.utils.translation import gettext_lazy as _
from gabbro.graphene.exceptions import BadRequest

from common.handlers.dataset_files import DatasetFilesLoader
from common.utils import (
    get_json_schema_errors,
    get_predicted_jsonschema,
    parse_all_properties,
)
from layers.models import Layer
from workspaces.enums import LongLatEnum
from workspaces.models import Dataset

DATASET_SAMPLE_ROWS_NUMBER = getattr(settings, "DATASET_SAMPLE_ROWS_NUMBER", 5)


def get_predicted_jsonschema_from_dataset(
    geometry_columns: list[str], file: str, sample: bool = True
):
    # Loading data from dataset file
    loader = DatasetFilesLoader(file=file, excluded_columns=geometry_columns)
    try:
        df = loader.load_data(
            rows_number=DATASET_SAMPLE_ROWS_NUMBER if sample else None
        )

    except Exception as e:
        raise BadRequest(reason={"error": _("Invalid dataset file") % {}})
    data = [parse_all_properties(record) for record in df.to_dict(orient="records")]

    # Get the predicted schema from dataset data
    json_schema = get_predicted_jsonschema(data=data, with_enums=len(data) > 20)
    if get_json_schema_errors(json_schema):
        json_schema = dict()

    return data, json_schema


def get_geometry_columns_from_location_field_mapping(location_field_mapping):
    if location_field_mapping["lat_lon_column_num"] == LongLatEnum.column.value:
        return [location_field_mapping["lang_lat_column"]]
    return [
        location_field_mapping["latitude_column"],
        location_field_mapping["longitude_column"],
    ]


def get_first_column_from_layer(dataset: Dataset):
    columns = dataset.meta_data.get("columns")
    return [columns[0]] if columns else []


def mapping_sample_data_with_json_schem_title(sample_data, json_schema):
    properties = json_schema.get("properties", dict())
    for data in sample_data:
        column = data.get("column")
        data["title"] = properties.get(column, dict()).get("title")
    return sample_data


def mapping_columns_with_json_schema_title(columns: list[str], json_schema: dict):
    properties = json_schema.get("properties", dict())
    return [(column, properties.get(column, dict()).get("title")) for column in columns]


def generate_json_schema_for_the_whole_dataset(
    layer: Layer, geometry_columns: list[str]
):
    # Generate the JSON Schema if not exist
    saved_json_schema = layer.json_schema
    _, predicted_json_schema = get_predicted_jsonschema_from_dataset(
        geometry_columns=geometry_columns, file=layer.dataset.file
    )
    if predicted_json_schema != saved_json_schema:
        return

    _, json_schema = get_predicted_jsonschema_from_dataset(
        geometry_columns=geometry_columns, file=layer.dataset.file, sample=False
    )

    layer.json_schema = json_schema
    layer.save(update_fields=["json_schema", "modified"])
    layer.dataset.set_metadata(dict(json_schema=json_schema))
