# Generated by Django 3.2 on 2025-08-14 00:19

from django.db import migrations


def update_workspaces_with_their_datasets(apps, schema_editor):
    Dataset = apps.get_model("workspaces", "Dataset")
    workspaces_datasets = dict()
    datasets = Dataset.objects.filter(workspace__isnull=False).select_related(
        "workspace"
    )
    for dataset in datasets:
        workspaces_datasets.setdefault(dataset.workspace, []).append(dataset)
        dataset.organization_id = dataset.workspace.organization_id
    Dataset.objects.bulk_update(datasets, ["organization"])

    for workspace, datasets in workspaces_datasets.items():
        workspace.datasets.add(*datasets)


class Migration(migrations.Migration):

    dependencies = [
        ("workspaces", "0025_auto_20250813_0715"),
    ]

    operations = [
        migrations.RunPython(
            update_workspaces_with_their_datasets, migrations.RunPython.noop
        ),
    ]
