# Generated by Django 3.2 on 2025-08-27 23:20

from django.db import migrations

from ._0009_constants import (
    ADD_WORKSPACE,
    CHANGE_WORKSPACE,
    VIEW_WORKSPACE,
    DELETE_WORKSPACE,
)


def delete_old_workspace_roles(apps, schema_editor):
    Role = apps.get_model("acl", "Role")
    old_workspace_roles = [
        "workspace-add",
        "workspace-change",
        "workspace-delete",
        "workspace-view",
    ]
    Role.objects.filter(codename__in=old_workspace_roles).delete()


def create_workspace_roles(apps, schema_editor):
    Permission = apps.get_model("auth", "Permission")
    ContentType = apps.get_model("contenttypes", "ContentType")
    Workspace = apps.get_model("workspaces", "Workspace")
    Role = apps.get_model("acl", "Role")
    workspace_permissions_mapper = {
        "workspace-admin": [
            ADD_WORKSPACE,
            CHANGE_WORKSPACE,
            DELETE_WORKSPACE,
            VIEW_WORKSPACE,
        ],
        "workspace-editor": [AD<PERSON>_WORKSPACE, <PERSON><PERSON><PERSON>_WORKSPACE, VIEW_WORKSPACE],
        "workspace-viewer": [VIEW_WORKSPACE],
    }
    workspace_content_type = ContentType.objects.get_for_model(Workspace)
    for role_name, codenames in workspace_permissions_mapper.items():
        permissions = [
            Permission.objects.get_or_create(
                codename=codename, content_type=workspace_content_type
            )[0]
            for codename in codenames
        ]
        for codename in codenames:
            Permission.objects.get_or_create(
                codename=codename, content_type=workspace_content_type
            )
        role, _ = Role.objects.get_or_create(
            codename=role_name,
            defaults={"title": role_name.title().replace("-", " ")},
        )
        role.permissions.set(permissions)


class Migration(migrations.Migration):

    dependencies = [
        ("organizations", "0018_create_default_organization_roles"),
    ]

    operations = [
        migrations.RunPython(delete_old_workspace_roles, migrations.RunPython.noop),
        migrations.RunPython(create_workspace_roles, migrations.RunPython.noop),
    ]
