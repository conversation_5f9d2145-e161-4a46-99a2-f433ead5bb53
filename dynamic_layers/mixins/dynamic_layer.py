"""
Dynamic Layer Mixin

This mixin provides functionality for creating and managing dynamic tables
for GIS layers.
"""

import logging
from typing import Any, Dict, List, Optional

from django.conf import settings
from django.db import transaction

from common.utils import calculate_boundaries_postgis
from dynamic_layers.handlers.gis_schema_analyzer import GISSchemaAnalyzer
from dynamic_layers.models import DynamicLayer
from dynamic_layers.utils.dynamic_data_importer import DynamicDataImporter
from dynamic_layers.utils.dynamic_table_manager import DynamicTableManager
from layers.models import Layer

logger = logging.getLogger("dynamic_layer_mixin")


class DynamicLayerMixin:
    """
    Mixin that provides dynamic table functionality for layers.
    """

    def create_dynamic_table_for_layer(
        self, layer: DynamicLayer, dataset_file_path: str
    ) -> bool:
        """
        Create a dynamic table for a layer based on its dataset file.

        Args:
            layer: Layer instance
            dataset_file_path: Path to the dataset file

        Returns:
            True if successful, False otherwise
        """
        try:
            logger.debug(f"Creating dynamic table for layer {layer.id}: {layer.title}")

            # Analyze the file schema
            schema_analyzer = GISSchemaAnalyzer(dataset_file_path)
            schema = schema_analyzer.analyze_file_schema()

            if not schema:
                logger.warning(
                    f"No schema found for layer {layer.id}, falling back to traditional storage"
                )
                return False

            # Create the dynamic model and table
            table_manager = DynamicTableManager()
            dynamic_model, model_name = table_manager.create_dynamic_model(
                layer_id=layer.id, layer_name=layer.title, schema=schema
            )

            # Create the database table
            success = table_manager.create_database_table(dynamic_model)

            if success:
                # Update the layer with dynamic table information
                layer.set_dynamic_table(model_name)
                return True
            else:
                logger.error(f"Failed to create database table for layer {layer.id}")
                return False

        except Exception as e:
            logger.error(f"Error creating dynamic table for layer {layer.id}: {e}")
            return False

    def import_data_to_dynamic_table(
        self,
        layer: DynamicLayer,
        features: List[Dict[str, Any]],
        geometry_columns: List[str],
    ) -> int:
        """
        Import feature data into a layer's dynamic table.

        Args:
            layer: Layer instance
            features: List of feature dictionaries
            geometry_columns: List of column names containing geometry data

        Returns:
            Number of successfully imported records
        """
        try:
            if not layer.has_dynamic_table():
                logger.warning(f"Layer {layer.id} does not have a dynamic table")
                return 0

            logger.debug(
                f"Importing {len(features)} features to dynamic table for layer {layer.id}"
            )

            # Use the dynamic data importer
            importer = DynamicDataImporter(layer)
            imported_count = importer.import_features(features, geometry_columns)

            logger.debug(
                f"Successfully imported {imported_count} records to layer {layer.id}"
            )
            return imported_count

        except Exception as e:
            logger.error(
                f"Error importing data to dynamic table for layer {layer.id}: {e}"
            )
            return 0

    def create_dynamic_records_from_dataset_file(
        self,
        layer: DynamicLayer,
        geometry_columns: List[str],
        summary_fields: List[str],
    ) -> int:
        """
        Create records in dynamic table from the dataset file.

        This method replaces the traditional create_geometry_records_from_dataset_file
        for layers using dynamic tables.

        Args:
            layer: Layer instance
            geometry_columns: List of column names containing geometry data
            summary_fields: List of summary field names

        Returns:
            Number of successfully imported records
        """
        try:
            from common.handlers.dataset_files import DatasetFilesLoader

            # Load all features from the dataset file
            loader = DatasetFilesLoader(file=layer.dataset.file)
            all_features = loader.get_all_features()

            if not all_features:
                logger.warning(
                    f"No features found in dataset file for layer {layer.id}"
                )
                return 0

            # Import features to dynamic table
            imported_count = self.import_data_to_dynamic_table(
                layer=layer, features=all_features, geometry_columns=geometry_columns
            )

            return imported_count

        except Exception as e:
            logger.error(f"Error creating dynamic records for layer {layer.id}: {e}")
            return 0

    def should_use_dynamic_table(self, dataset_file_path: str) -> bool:
        """
        Determine if a dataset should use dynamic table storage.

        Args:
            dataset_file_path: Path to the dataset file

        Returns:
            True if dynamic table should be used, False otherwise
        """
        try:
            # Check if dynamic tables are enabled (could be a setting)
            if not getattr(settings, "ENABLE_DYNAMIC_TABLES", True):
                return False

            # Analyze the file to see if it has structured properties
            schema_analyzer = GISSchemaAnalyzer(dataset_file_path)
            schema = schema_analyzer.analyze_file_schema()

            # Use dynamic table if we have at least some structured properties
            return len(schema) > 0

        except Exception as e:
            logger.warning(f"Error determining if dynamic table should be used: {e}")
            return False

    def get_dynamic_table_statistics(self, layer: DynamicLayer) -> Dict[str, Any]:
        """
        Get statistics about a layer's dynamic table.

        Args:
            layer: Layer instance

        Returns:
            Dictionary with statistics
        """
        if not layer.has_dynamic_table():
            return {}

        try:
            dynamic_model = layer.get_dynamic_model()
            if not dynamic_model:
                return {}

            record_count = dynamic_model.objects.filter(layer_id=layer.id).count()

            return {
                "table_name": layer.dynamic_table_name,
                "record_count": record_count,
                "field_count": len(
                    [
                        f
                        for f in dynamic_model._meta.fields
                        if f.name
                        not in ["id", "layer_id", "geometry", "created", "modified"]
                    ]
                ),
            }

        except Exception as e:
            logger.error(
                f"Error getting dynamic table statistics for layer {layer.id}: {e}"
            )
            return {}

    def migrate_layer_to_dynamic_table(self, layer: Layer) -> bool:
        """
        Migrate an existing layer from JSON storage to dynamic table.

        Args:
            layer: Layer instance to migrate

        Returns:
            True if successful, False otherwise
        """
        try:
            # Check if layer has records to migrate
            if not layer.records.exists():
                logger.debug(f"Layer {layer.id} has no records to migrate")
                return True

            logger.debug(f"Starting migration of layer {layer.id} to dynamic table")

            # Analyze existing record properties to create schema
            schema = self._analyze_existing_records_schema(layer)

            if not schema:
                logger.warning(f"Could not determine schema for layer {layer.id}")
                return False

            # Create dynamic table
            table_manager = DynamicTableManager()
            dynamic_model, model_name = table_manager.create_dynamic_model(
                layer_id=layer.id, layer_name=layer.title, schema=schema
            )

            success = table_manager.create_database_table(dynamic_model)

            if not success:
                logger.error(f"Failed to create dynamic table for layer {layer.id}")
                return False

            # Migrate existing records
            migrated_count = self._migrate_existing_records(layer, dynamic_model)

            if migrated_count > 0:
                # Update layer to use dynamic table
                layer.set_dynamic_table(model_name)

                # Model is automatically registered by django-dynamic-model

                logger.debug(
                    f"Successfully migrated {migrated_count} records for layer {layer.id}"
                )
                return True
            else:
                # Clean up the table if migration failed
                table_manager.drop_dynamic_table(dynamic_model._meta.db_table)
                logger.error(f"Failed to migrate records for layer {layer.id}")
                return False

        except Exception as e:
            logger.error(f"Error migrating layer {layer.id} to dynamic table: {e}")
            return False

    def _analyze_existing_records_schema(
        self, layer: Layer
    ) -> Dict[str, Dict[str, Any]]:
        """
        Analyze existing records to determine schema for dynamic table.

        Args:
            layer: Layer instance

        Returns:
            Schema definition dictionary
        """
        try:
            # Sample some records to analyze their properties
            sample_records = layer.records.all()[:100]

            if not sample_records:
                return {}

            # Collect all property keys and sample values
            all_properties = {}
            for record in sample_records:
                properties = record.source_properties or {}
                for key, value in properties.items():
                    if key not in all_properties:
                        all_properties[key] = []
                    all_properties[key].append(value)

            # Analyze each property to determine field type
            schema = {}
            for prop_name, values in all_properties.items():
                field_def = self._infer_field_type_from_values(values)
                if field_def:
                    # Sanitize property name
                    sanitized_name = self._sanitize_property_name(prop_name)
                    schema[sanitized_name] = field_def

            return schema

        except Exception as e:
            logger.error(f"Error analyzing existing records schema: {e}")
            return {}

    def _infer_field_type_from_values(
        self, values: List[Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Infer Django field type from a list of values.

        Args:
            values: List of values to analyze

        Returns:
            Field definition dictionary
        """
        # Filter out None values
        non_null_values = [v for v in values if v is not None]

        if not non_null_values:
            return {"type": "CharField", "max_length": 255, "null": True, "blank": True}

        # Try to determine type
        if all(isinstance(v, bool) for v in non_null_values):
            return {"type": "BooleanField", "null": True, "blank": True}
        elif all(isinstance(v, int) for v in non_null_values):
            return {"type": "IntegerField", "null": True, "blank": True}
        elif all(isinstance(v, (int, float)) for v in non_null_values):
            return {"type": "FloatField", "null": True, "blank": True}
        else:
            # Default to CharField with appropriate max_length
            max_length = max(len(str(v)) for v in non_null_values)
            max_length = min(max(max_length, 50), 255)  # Between 50 and 255
            return {
                "type": "CharField",
                "max_length": max_length,
                "null": True,
                "blank": True,
            }

    def _sanitize_property_name(self, prop_name: str) -> str:
        """Sanitize property name for use as Django field name."""
        import re

        sanitized = re.sub(r"[^a-zA-Z0-9_]", "_", prop_name.lower())
        if sanitized and sanitized[0].isdigit():
            sanitized = f"field_{sanitized}"
        return sanitized or "unnamed_field"

    def _migrate_existing_records(self, layer: Layer, dynamic_model) -> int:
        """
        Migrate existing records from JSON storage to dynamic table.

        Args:
            layer: Layer instance
            dynamic_model: Dynamic model class

        Returns:
            Number of migrated records
        """
        try:
            migrated_count = 0
            field_names = [f.name for f in dynamic_model._meta.fields]

            # Process records in batches
            batch_size = getattr(settings, "RECORDS_BATCH_SIZE", 1000)

            for batch_start in range(0, layer.records.count(), batch_size):
                records_batch = layer.records.all()[
                    batch_start : batch_start + batch_size
                ]
                dynamic_records = []

                for record in records_batch:
                    try:
                        # Create dynamic record
                        record_data = {
                            "layer_id": layer.id,
                            "geometry": record.geometry,
                        }

                        # Add properties as individual fields
                        properties = record.source_properties or {}
                        for key, value in properties.items():
                            sanitized_key = self._sanitize_property_name(key)
                            if sanitized_key in field_names:
                                record_data[sanitized_key] = value

                        dynamic_records.append(dynamic_model(**record_data))

                    except Exception as e:
                        logger.debug(f"Error migrating record {record.id}: {e}")

                # Bulk create the batch
                if dynamic_records:
                    with transaction.atomic():
                        dynamic_model.objects.bulk_create(
                            dynamic_records, ignore_conflicts=True
                        )
                    migrated_count += len(dynamic_records)

            return migrated_count

        except Exception as e:
            logger.error(f"Error migrating existing records: {e}")
            return 0

    def update_layer_boundaries_dynamic(self, layer: DynamicLayer) -> None:
        """
        Update layer boundaries for layers using dynamic tables.

        Args:
            layer: Layer instance
        """
        try:
            # Use dynamic table for boundary calculation
            dynamic_model = layer.get_dynamic_model()
            if dynamic_model:
                # Get geometry records from dynamic table
                geometry_records = dynamic_model.objects.filter(layer_id=layer.id).only(
                    "geometry"
                )

                layer.boundaries = calculate_boundaries_postgis(geometry_records)
                layer.save(update_fields=["boundaries", "modified"])
                logger.debug(f"Updated boundaries for dynamic layer {layer.id}")
                return

        except Exception as e:
            logger.error(f"Error updating layer boundaries for layer {layer.id}: {e}")
