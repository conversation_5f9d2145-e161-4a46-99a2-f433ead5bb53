"""
Dynamic Data Importer

This module handles importing GIS feature data into dynamically created tables.
"""

import logging
from typing import Any, Dict, List, Optional

from django.conf import settings
from django.db import models
from django.utils.dateparse import parse_date, parse_datetime
from shapely.geometry.base import BaseGeometry
from shapely.geometry.geo import mapping
from shapely.geometry.point import Point

from common.utils import get_valid_geometry, slice_list_to_chunks
from dynamic_layers.models import DynamicLayer

logger = logging.getLogger("dynamic_data_importer")


class DynamicDataImporter:
    """
    Handles importing GIS feature data into dynamic tables.
    """

    def __init__(self, layer: DynamicLayer):
        """
        Initialize the importer for a specific layer.
        """
        self.layer = layer
        self.dynamic_model = self.layer.get_dynamic_model()

        if not self.dynamic_model:
            raise ValueError(f"No dynamic model found for layer {layer}")

    def import_features(
        self, features: List[Dict[str, Any]], geometry_columns: List[str]
    ) -> int:
        """
        Import GIS features into the dynamic table.

        Args:
            features: List of feature dictionaries from GIS file
            geometry_columns: List of column names that contain geometry data

        Returns:
            Number of successfully imported records
        """
        logger.debug(
            f"Starting import of {len(features)} features for layer {self.layer}"
        )

        total_imported = 0

        # Process features in chunks
        for feature_chunk in slice_list_to_chunks(
            features, chunk_size=settings.RECORDS_BATCH_SIZE
        ):
            imported_count = self._import_feature_chunk(feature_chunk, geometry_columns)
            total_imported += imported_count

        logger.debug(
            f"Successfully imported {total_imported} records for layer {self.layer}"
        )
        return total_imported

    def _import_feature_chunk(
        self, features: List[Dict[str, Any]], geometry_columns: List[str]
    ) -> int:
        """
        Import a chunk of features into the dynamic table.

        Args:
            features: List of feature dictionaries
            geometry_columns: List of column names that contain geometry data

        Returns:
            Number of successfully imported records in this chunk
        """
        records = []
        invalid_records = []

        for i, feature in enumerate(features):
            try:
                record = self._create_record_from_feature(feature, geometry_columns)
                if record:
                    records.append(record)
                else:
                    invalid_records.append((i, feature, "Failed to create record"))
            except Exception as e:
                invalid_records.append((i, feature, str(e)))
                logger.debug(f"Failed to process feature {i}: {e}")

        if invalid_records:
            logger.warning(f"Found {len(invalid_records)} invalid records in chunk")

        # Bulk create records
        if records:
            try:
                self.dynamic_model.objects.bulk_create(
                    records,
                    batch_size=settings.RECORDS_BATCH_SIZE,
                    ignore_conflicts=True,
                )
                logger.debug(f"Successfully imported {len(records)} records")
                return len(records)
            except Exception as e:
                logger.error(f"Failed to bulk create records: {e}")
                return 0

        return 0

    def _create_record_from_feature(
        self, feature: Dict[str, Any], geometry_columns: List[str]
    ) -> Optional[models.Model]:
        """
        Create a dynamic model instance from a GIS feature.

        Args:
            feature: Feature dictionary from GIS file
            geometry_columns: List of column names that contain geometry data

        Returns:
            Dynamic model instance or None if creation fails
        """
        try:
            # Extract and process geometry
            geometry_geojson = self._get_geojson_from_feature(feature, geometry_columns)
            geometry = get_valid_geometry(geometry_geojson)

            if not geometry or not geometry.valid:
                logger.debug("Invalid or missing geometry in feature")
                return None

            # Remove geometry columns from feature properties
            feature_copy = feature.copy()
            for col in geometry_columns:
                feature_copy.pop(col, None)

            # Prepare record data
            record_data = {
                "layer_id": self.layer.id,
                "geometry": geometry,
            }

            # Add feature properties as individual columns
            field_names = [f.name for f in self.dynamic_model._meta.fields]
            for key, value in feature_copy.items():
                # Sanitize field name to match model field
                sanitized_key = self._sanitize_field_name(key)

                if sanitized_key in field_names:
                    # Convert value to appropriate type
                    converted_value = self._convert_value_for_field(
                        sanitized_key, value
                    )
                    record_data[sanitized_key] = converted_value

            # Create the record instance
            return self.dynamic_model(**record_data)

        except Exception as e:
            logger.debug(f"Error creating record from feature: {e}")
            return None

    def _get_geojson_from_feature(
        self, feature: Dict[str, Any], geometry_columns: List[str]
    ) -> Dict[str, Any]:
        """
        Extract geometry data from feature in GeoJSON format.

        Args:
            feature: Feature dictionary
            geometry_columns: List of column names that contain geometry data

        Returns:
            GeoJSON geometry dictionary
        """

        if len(geometry_columns) == 1:
            geometry = feature.get(geometry_columns[0])
        else:
            # Assume lat/lon columns
            lon = feature.get(geometry_columns[0])
            lat = feature.get(geometry_columns[1])
            if lon is not None and lat is not None:
                geometry = Point(float(lon), float(lat))
            else:
                geometry = None

        if isinstance(geometry, BaseGeometry):
            return mapping(geometry)
        elif isinstance(geometry, dict):
            return geometry
        else:
            raise ValueError(f"Invalid geometry type: {type(geometry)}")

    def _sanitize_field_name(self, field_name: str) -> str:
        """
        Sanitize field name to match the model field naming convention.

        Args:
            field_name: Original field name

        Returns:
            Sanitized field name
        """
        import re

        # Convert to lowercase and replace invalid characters
        sanitized = re.sub(r"[^a-zA-Z0-9_]", "_", field_name.lower())

        # Ensure it starts with a letter or underscore
        if sanitized and sanitized[0].isdigit():
            sanitized = f"field_{sanitized}"

        # Ensure it's not empty
        if not sanitized:
            sanitized = "unnamed_field"

        return sanitized

    def _convert_value_for_field(self, field_name: str, value: Any) -> Any:
        """
        Convert a value to the appropriate type for a model field.

        Args:
            field_name: Name of the model field
            value: Value to convert

        Returns:
            Converted value
        """
        if value is None or value == "":
            return None

        try:
            # Get the field from the model
            field = self.dynamic_model._meta.get_field(field_name)

            # Convert based on field type
            if isinstance(field, models.CharField):
                return str(value)
            elif isinstance(field, models.IntegerField):
                return int(float(value))  # Handle cases like "123.0"
            elif isinstance(field, models.FloatField):
                return float(value)
            elif isinstance(field, models.BooleanField):
                if isinstance(value, bool):
                    return value
                elif isinstance(value, str):
                    return value.lower() in ("true", "1", "yes", "y", "on")
                else:
                    return bool(value)
            elif isinstance(field, models.DateField):
                if isinstance(value, str):
                    return parse_date(value)
                return value
            elif isinstance(field, models.DateTimeField):
                if isinstance(value, str):
                    return parse_datetime(value)
                return value
            else:
                return str(value)  # Default to string

        except Exception as e:
            logger.debug(f"Error converting value {value} for field {field_name}: {e}")
            return None

    def get_import_statistics(self) -> Dict[str, int]:
        """
        Get statistics about the imported data.

        Returns:
            Dictionary with import statistics
        """
        if not self.dynamic_model:
            return {}

        try:
            total_records = self.dynamic_model.objects.filter(
                layer_id=self.layer.id
            ).count()
            return {
                "total_records": total_records,
                "layer_id": self.layer.id,
                "table_name": self.dynamic_model._meta.db_table,
            }
        except Exception as e:
            logger.error(f"Error getting import statistics: {e}")
            return {}
