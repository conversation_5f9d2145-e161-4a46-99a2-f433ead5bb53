"""
Dynamic Table Manager for GIS Layers

This module handles the creation and management of dynamic database tables
for GIS layers using the django-dynamic-model package.
"""

import logging
import re
from typing import Dict, Any, Optional, Type, Tuple

from django.contrib.gis.db import models
from django.db import connection
from dynamic_models.factory import ModelFactory
from dynamic_models.models import ModelSchema, FieldSchema

logger = logging.getLogger("dynamic_table_manager")


class DynamicTableManager:
    """
    Manages creation and lifecycle of dynamic tables for GIS layers using django-dynamic-model.
    """

    TABLE_PREFIX = "dynamic_layer"
    MAX_TABLE_NAME_LENGTH = 63  # PostgreSQL limit

    def create_dynamic_model(
        self,
        layer_id: int,
        layer_name: str,
        schema: Dict[str, Dict[str, Any]],
    ) -> Tuple[Optional[Type[models.Model]], str]:
        """
        Create a dynamic Django model class for a GIS layer using django-dynamic-model.

        Args:
            layer_id: ID of the layer
            layer_name: Name of the layer (used for table naming)
            schema: Schema definition from GISSchemaAnalyzer

        Returns:
            Django model class
        """
        logger.debug(f"Creating dynamic model for layer {layer_id}: {layer_name}")

        # Generate table name and model name
        table_name = self._generate_table_name(layer_id, layer_name)
        model_name = self._generate_model_name(layer_id, layer_name)

        try:
            # Check if ModelSchema already exists
            model_schema, created = ModelSchema.objects.get_or_create(name=model_name)

            if created:
                logger.debug(f"Created new ModelSchema: {model_name}")

                # Create field schemas
                self._create_field_schemas(model_schema, schema, layer_id)
            else:
                logger.debug(f"Using existing ModelSchema: {model_name}")

            # Create the dynamic model using ModelFactory
            model_class = ModelFactory(model_schema).get_model()

            logger.debug(f"Created dynamic model: {model_name} -> {table_name}")
            return model_class, model_name

        except Exception as e:
            logger.error(f"Error creating dynamic model for layer {layer_id}: {e}")
            raise

    def _create_field_schemas(
        self,
        model_schema: ModelSchema,
        schema: Dict[str, Dict[str, Any]],
        layer_id: int,
    ):
        """
        Create FieldSchema objects for the ModelSchema.

        Args:
            model_schema: The ModelSchema instance
            schema: Schema definition from GISSchemaAnalyzer
            layer_id: ID of the layer
        """
        # Add standard GIS fields
        standard_fields = [
            {
                "name": "layer_id",
                "class_name": "integer",
                "kwargs": {"null": False, "blank": False},
            },
            {
                "name": "geometry",
                "class_name": "geometry",
                "kwargs": {"null": True, "blank": True},
            },
            {"name": "created", "class_name": "date", "kwargs": {"auto_now_add": True}},
            {"name": "modified", "class_name": "date", "kwargs": {"auto_now": True}},
        ]

        # Add standard fields
        for field_def in standard_fields:
            field_kwargs = field_def["kwargs"]
            FieldSchema.objects.get_or_create(
                model_schema=model_schema,
                name=field_def["name"],
                defaults={
                    "data_type": field_def["class_name"],
                    "null": field_kwargs.get("null", True),
                    "unique": field_kwargs.get("unique", False),
                    "max_length": field_kwargs.get("max_length", None),
                },
            )

        # Add dynamic fields from schema
        for field_name, field_config in schema.items():
            django_field_config = self._convert_schema_to_django_field(field_config)
            if django_field_config:
                field_kwargs = django_field_config.get("kwargs", {})
                FieldSchema.objects.get_or_create(
                    model_schema=model_schema,
                    name=field_name,
                    defaults={
                        "data_type": django_field_config["class_name"],
                        "null": field_kwargs.get("null", True),
                        "unique": field_kwargs.get("unique", False),
                        "max_length": field_kwargs.get("max_length", None),
                    },
                )

    def _convert_schema_to_django_field(
        self, field_config: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Convert schema field config to django-dynamic-model format.

        Args:
            field_config: Field configuration from schema

        Returns:
            Dictionary with class_name and kwargs for FieldSchema
        """
        field_type = field_config.get("type")

        # Map field types to django-dynamic-model field types
        type_mapping = {
            "CharField": "character",
            "TextField": "text",
            "IntegerField": "integer",
            "BigIntegerField": "integer",
            "FloatField": "float",
            "DecimalField": "float",
            "BooleanField": "boolean",
            "DateField": "date",
            "DateTimeField": "date",
            "TimeField": "character",  # No native time type, use character
            "URLField": "character",
            "EmailField": "character",
            "SlugField": "character",
        }

        class_name = type_mapping.get(field_type)
        if not class_name:
            logger.warning(f"Unknown field type: {field_type}, defaulting to CharField")
            class_name = "CharField"

        # Build kwargs from field config
        kwargs = {}
        for attr in ["max_length", "null", "blank", "default", "help_text"]:
            if attr in field_config:
                kwargs[attr] = field_config[attr]

        # Set default null/blank for most fields
        if "null" not in kwargs:
            kwargs["null"] = True
        if "blank" not in kwargs:
            kwargs["blank"] = True

        return {"class_name": class_name, "kwargs": kwargs}

    def create_database_table(self, model_class: Type[models.Model]) -> bool:
        """
        Create the actual database table for a dynamic model.

        With django-dynamic-model, the table is automatically created when the model is accessed.
        This method ensures the table exists and creates additional indexes.

        Args:
            model_class: Django model class to create table for

        Returns:
            True if successful, False otherwise
        """
        table_name = model_class._meta.db_table
        logger.debug(f"Ensuring database table exists: {table_name}")
        # With django-dynamic-model, the table is created automatically.
        # We just need to ensure it exists by accessing the model
        model_class._meta.get_field("id")  # This triggers table creation if needed

        try:
            # Create spatial index for geometry field
            self._create_spatial_index(table_name)

            # Create additional indexes for commonly queried fields
            self._create_additional_indexes(model_class)

            logger.debug(f"Successfully ensured table exists: {table_name}")
            return True

        except Exception as e:
            logger.error(f"Error ensuring table {table_name} exists: {e}")
            return False

    def drop_dynamic_table(self, table_name: str) -> bool:
        """
        Drop a dynamic table from the database.

        Args:
            table_name: Name of the table to drop

        Returns:
            True if successful, False otherwise
        """
        try:
            logger.debug(f"Dropping dynamic table: {table_name}")

            with connection.cursor() as cursor:
                cursor.execute(f'DROP TABLE IF EXISTS "{table_name}" CASCADE')

            logger.debug(f"Successfully dropped table: {table_name}")
            return True

        except Exception as e:
            logger.error(f"Failed to drop table {table_name}: {e}")
            return False

    def table_exists(self, table_name: str) -> bool:
        """
        Check if a dynamic table exists in the database.

        Args:
            table_name: Name of the table to check

        Returns:
            True if table exists, False otherwise
        """
        try:
            with connection.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = %s
                    )
                """,
                    [table_name],
                )
                return cursor.fetchone()[0]
        except Exception as e:
            logger.error(f"Error checking table existence for {table_name}: {e}")
            return False

    def get_table_schema(self, table_name: str) -> Dict[str, Dict[str, Any]]:
        """
        Get the schema of an existing dynamic table.

        Args:
            table_name: Name of the table

        Returns:
            Schema definition dictionary
        """
        try:
            with connection.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT column_name, data_type, is_nullable, character_maximum_length,
                           numeric_precision, numeric_scale
                    FROM information_schema.columns 
                    WHERE table_name = %s
                    ORDER BY ordinal_position
                """,
                    [table_name],
                )

                columns = cursor.fetchall()
                schema = {}

                for (
                    col_name,
                    data_type,
                    is_nullable,
                    max_length,
                    precision,
                    scale,
                ) in columns:
                    if col_name in [
                        "id",
                        "layer_id",
                        "geometry",
                        "created",
                        "modified",
                    ]:
                        continue  # Skip base fields

                    schema[col_name] = self._db_type_to_django_field(
                        data_type, is_nullable == "YES", max_length
                    )

                return schema

        except Exception as e:
            logger.error(f"Error getting schema for table {table_name}: {e}")
            return {}

    def _generate_table_name(self, layer_id: int, layer_name: str) -> str:
        """Generate a valid database table name."""
        # Sanitize layer name
        sanitized_name = re.sub(r"[^a-zA-Z0-9_]", "_", layer_name.lower())
        sanitized_name = re.sub(
            r"_+", "_", sanitized_name
        )  # Remove multiple underscores
        sanitized_name = sanitized_name.strip(
            "_"
        )  # Remove leading/trailing underscores

        if not sanitized_name:
            sanitized_name = "layer"

        # Create the table name
        table_name = f"{self.TABLE_PREFIX}_{layer_id}_{sanitized_name}"

        # Truncate if too long
        if len(table_name) > self.MAX_TABLE_NAME_LENGTH:
            # Keep the layer_id part and truncate the name part
            prefix_part = f"{self.TABLE_PREFIX}_{layer_id}_"
            remaining_length = self.MAX_TABLE_NAME_LENGTH - len(prefix_part)
            table_name = prefix_part + sanitized_name[:remaining_length]

        return table_name

    def _generate_model_name(self, layer_id: int, layer_name: str) -> str:
        """
        Generate a valid Django model class name.

        Django-dynamic-model has a 32-character limit for model names.
        """
        # Simple approach: just use layer_id to ensure uniqueness and stay within limit
        model_name = f"DynamicLayer{layer_id}"

        # Ensure we don't exceed 32 characters (shouldn't happen with reasonable layer IDs)
        if len(model_name) > 32:
            model_name = model_name[:32]

        return model_name

    def _build_model_attributes(
        self, schema: Dict[str, Dict[str, Any]], table_name: str
    ) -> Dict[str, Any]:
        """Build Django model attributes from schema definition."""
        attrs = {
            "__module__": "layers.models.dynamic_layer",
            "Meta": type(
                "Meta",
                (),
                {
                    "db_table": table_name,
                    "managed": True,
                    "indexes": [
                        models.Index(
                            fields=["layer_id"], name=f"{table_name[:50]}_layer_idx"
                        ),
                    ],
                },
            ),
        }

        # Add fields based on schema
        for field_name, field_config in schema.items():
            django_field = self._create_django_field(field_config)
            if django_field:
                attrs[field_name] = django_field

        return attrs

    def _create_django_field(
        self, field_config: Dict[str, Any]
    ) -> Optional[models.Field]:
        """Create a Django field from field configuration."""
        field_type = field_config.get("type")

        if not field_type:
            return None

        # Get the Django field class
        field_class = getattr(models, field_type, None)
        if not field_class:
            logger.warning(f"Unknown field type: {field_type}")
            return None
        # Build field kwargs
        field_kwargs = {k: v for k, v in field_config.items() if k != "type"}

        try:
            return field_class(**field_kwargs)
        except Exception as e:
            logger.error(
                f"Error creating field {field_type} with kwargs {field_kwargs}: {e}"
            )
            return None

    def _create_spatial_index(self, table_name: str) -> None:
        """Create spatial index for geometry field."""
        try:
            index_name = f"{table_name}_geometry_idx"
            with connection.cursor() as cursor:
                cursor.execute(
                    f"""
                    CREATE INDEX IF NOT EXISTS "{index_name}" 
                    ON "{table_name}" USING GIST (geometry)
                """
                )
            logger.debug(f"Created spatial index: {index_name}")
        except Exception as e:
            logger.warning(f"Failed to create spatial index for {table_name}: {e}")

    def _create_additional_indexes(self, model_class: Type[models.Model]) -> None:
        """Create additional indexes for commonly queried fields."""
        try:
            table_name = model_class._meta.db_table

            # Create indexes for string fields that might be used for filtering
            for field in model_class._meta.fields:
                if isinstance(field, models.CharField) and field.name not in [
                    "id",
                    "layer_id",
                ]:
                    index_name = f"{table_name}_{field.name}_idx"
                    # Truncate index name if too long
                    index_name = index_name[:63]

                    with connection.cursor() as cursor:
                        cursor.execute(
                            f"""
                            CREATE INDEX IF NOT EXISTS "{index_name}" 
                            ON "{table_name}" ("{field.name}")
                        """
                        )
                    logger.debug(f"Created index: {index_name}")

        except Exception as e:
            logger.warning(f"Failed to create additional indexes: {e}")

    def _db_type_to_django_field(
        self, data_type: str, is_nullable: bool, max_length: Optional[int] = None
    ) -> Dict[str, Any]:
        """Convert database type to Django field configuration."""
        data_type = data_type.lower()

        field_config = {"null": is_nullable, "blank": True}

        if data_type in ["character varying", "varchar", "text"]:
            field_config["type"] = "CharField"
            if max_length:
                field_config["max_length"] = max_length
            else:
                field_config["max_length"] = 255
        elif data_type in ["integer", "int4"]:
            field_config["type"] = "IntegerField"
        elif data_type in ["bigint", "int8"]:
            field_config["type"] = "BigIntegerField"
        elif data_type in ["real", "float4", "double precision", "float8", "numeric"]:
            field_config["type"] = "FloatField"
        elif data_type == "boolean":
            field_config["type"] = "BooleanField"
        elif data_type == "date":
            field_config["type"] = "DateField"
        elif data_type in ["timestamp", "timestamptz"]:
            field_config["type"] = "DateTimeField"
        else:
            field_config["type"] = "CharField"
            field_config["max_length"] = 255

        return field_config

        # Continue without saving - the system can still work without stored attrs

    def create_dynamic_model_from_attrs(
        self, layer_id: int, attrs: Dict[str, Any]
    ) -> Optional[Type[models.Model]]:
        """
        Create a dynamic model from stored attributes.

        Args:
            layer_id: ID of the layer
            attrs: Stored model attributes

        Returns:
            Django model class or None if creation fails
        """
        try:
            # Reconstruct the model attributes
            reconstructed_attrs = {}

            for key, value in attrs.items():
                if key == "__module__":
                    reconstructed_attrs[key] = value
                elif key == "Meta":
                    # Reconstruct Meta class
                    meta_attrs = value
                    meta_attrs["indexes"] = [
                        models.Index(fields=idx["fields"], name=idx["name"])
                        for idx in value.get("indexes", [])
                    ]
                    meta_class = type("Meta", (), meta_attrs)
                    reconstructed_attrs[key] = meta_class
                elif isinstance(value, dict) and "field_type" in value:
                    # Reconstruct Django field
                    field_type = value["field_type"]
                    field_module = value.get("field_module", "django.db.models")
                    field_attrs = value.get("attrs", {})

                    # Import the field class
                    if field_module == "django.db.models":
                        field_class = getattr(models, field_type, None)
                    else:
                        # Handle other modules if needed
                        field_class = getattr(models, field_type, None)

                    if field_class:
                        reconstructed_attrs[key] = field_class(**field_attrs)
                else:
                    reconstructed_attrs[key] = value

            # Generate model name
            model_name = f"DynamicLayer{layer_id}Reconstructed"

            # Create the model class
            model_class = type(model_name, (DynamicLayerBase,), reconstructed_attrs)

            logger.debug(
                f"Reconstructed dynamic model for layer {layer_id} from stored attrs"
            )
            return model_class

        except Exception as e:
            logger.error(
                f"Failed to reconstruct model from attrs for layer {layer_id}: {e}"
            )
            return None


class DynamicLayerBase(models.Model):
    """
    Base class for all dynamic layer tables.
    """

    layer_id = models.IntegerField(
        db_index=True, help_text="Reference to the Layer model"
    )
    geometry = models.GeometryField(srid=4326, help_text="Geometry data")
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True

    def __str__(self):
        return f"Dynamic record for layer {self.layer_id} (ID: {self.pk})"
