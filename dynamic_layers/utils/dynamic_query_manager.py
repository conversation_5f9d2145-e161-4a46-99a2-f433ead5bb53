"""
Dynamic Query Manager for GraphQL

This module handles queries on dynamic layer tables with GraphQL integration.
"""

import logging
from typing import Any, Dict, List, Optional, Tuple

from django.db.models import Q, QuerySet

from common.utils import build_advanced_q, filter_qs_paginate_with_count
from dynamic_layers.models import DynamicLayer
from layers.models import Record

logger = logging.getLogger("dynamic_query_manager")


class DynamicQueryManager:
    """
    Manages queries on dynamic layer tables for GraphQL resolvers.
    """

    def __init__(self, layer: DynamicLayer):
        """
        Initialize the query manager for a specific layer.
        """
        self.layer = layer
        self.dynamic_model = None
        self._load_layer_info()

    def _load_layer_info(self):
        """Load layer information and determine storage type."""

        self.dynamic_model = self.layer.get_dynamic_model()
        if not self.dynamic_model:
            logger.warning(
                f"Layer {self.layer > id} uses dynamic table but model not found"
            )

    def query_records(
        self,
        pk: Optional[int] = None,
        page_info: Optional[Dict[str, Any]] = None,
        filters: Optional[List[Dict[str, Any]]] = None,
        filter_groups: Optional[List[Dict[str, Any]]] = None,
        auto_complete: bool = False,
    ) -> Tuple[QuerySet, int]:
        """
        Query records from either dynamic table or traditional storage.

        Args:
            pk: Primary key filter
            page_info: Pagination information
            filters: Simple filters list
            filter_groups: Advanced filter groups
            auto_complete: Whether this is for autocomplete

        Returns:
            Tuple of (queryset, total_count)
        """
        if self.dynamic_model:
            return self._query_dynamic_records(
                pk, page_info, filters, filter_groups, auto_complete
            )
        else:
            return self._query_traditional_records(
                pk, page_info, filters, filter_groups, auto_complete
            )

    def _query_dynamic_records(
        self,
        pk: Optional[int] = None,
        page_info: Optional[Dict[str, Any]] = None,
        filters: Optional[List[Dict[str, Any]]] = None,
        filter_groups: Optional[List[Dict[str, Any]]] = None,
        auto_complete: bool = False,
    ) -> Tuple[QuerySet, int]:
        """Query records from dynamic table."""
        logger.debug(f"Querying dynamic table for layer {self.layer.id}")

        # Start with all records for this layer
        queryset = self.dynamic_model.objects.filter(layer_id=self.layer.id)

        # Build query filters
        if filter_groups:
            # Validate filter fields against dynamic model
            validated_filter_groups = self._validate_filter_groups(filter_groups)
            q_obj = build_advanced_q(validated_filter_groups, pk)

            if auto_complete:
                # Extract distinct fields for autocomplete
                fields = [
                    filter_item["field"]
                    for group in validated_filter_groups
                    for filter_item in group["filters"]
                ]
                # Only use fields that exist in the dynamic model
                valid_fields = [field for field in fields if self._field_exists(field)]
                if valid_fields:
                    queryset = queryset.distinct(*valid_fields)
        else:
            q_obj = self._build_simple_q(pk, filters)

        # Apply filters and pagination
        return filter_qs_paginate_with_count(queryset, q_obj, page_info)

    def _query_traditional_records(
        self,
        pk: Optional[int] = None,
        page_info: Optional[Dict[str, Any]] = None,
        filters: Optional[List[Dict[str, Any]]] = None,
        filter_groups: Optional[List[Dict[str, Any]]] = None,
        auto_complete: bool = False,
    ) -> Tuple[QuerySet, int]:
        """Query records from traditional Record table."""
        logger.debug(f"Querying traditional records for layer {self.layer.id}")

        # Use existing Record model query logic
        queryset = Record.objects.filter(layer_id=self.layer.id)

        # Build query filters (existing logic)
        if filter_groups:
            q_obj = build_advanced_q(filter_groups, pk)
            if auto_complete:
                fields = [
                    filter_item["field"]
                    for group in filter_groups
                    for filter_item in group["filters"]
                ]
                if fields:
                    queryset = queryset.distinct(*fields)
        else:
            q_obj = self._build_simple_q_traditional(pk, filters)

        return filter_qs_paginate_with_count(queryset, q_obj, page_info)

    def _validate_filter_groups(
        self, filter_groups: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Validate filter groups against dynamic model fields.

        Args:
            filter_groups: List of filter group dictionaries

        Returns:
            Validated filter groups with invalid fields removed
        """
        validated_groups = []

        for group in filter_groups:
            validated_filters = []

            for filter_item in group.get("filters", []):
                field_name = filter_item.get("field")

                if self._field_exists(field_name):
                    validated_filters.append(filter_item)
                else:
                    logger.warning(
                        f"Field '{field_name}' not found in dynamic model for layer {self.layer.id}"
                    )

            if validated_filters:
                validated_group = group.copy()
                validated_group["filters"] = validated_filters
                validated_groups.append(validated_group)

        return validated_groups

    def _field_exists(self, field_path: str) -> bool:
        """
        Check if a field path exists in the dynamic model.

        Args:
            field_path: Field path (e.g., "field_name" or "related__field")

        Returns:
            True if field exists, False otherwise
        """
        if not self.dynamic_model or not field_path:
            return False

        try:
            # Handle nested field paths
            field_parts = field_path.split("__")
            current_model = self.dynamic_model

            for part in field_parts:
                if part in ["pk", "id"]:
                    continue

                # Check if field exists in current model
                field_names = [f.name for f in current_model._meta.fields]
                if part not in field_names:
                    return False

                # For related fields, get the related model
                try:
                    field = current_model._meta.get_field(part)
                    if hasattr(field, "related_model") and field.related_model:
                        current_model = field.related_model
                except:
                    return False

            return True

        except Exception as e:
            logger.debug(f"Error checking field existence for '{field_path}': {e}")
            return False

    def _build_simple_q(
        self, pk: Optional[int], filters: Optional[List[Dict[str, Any]]]
    ) -> Q:
        """Build Q object for simple filters on dynamic model."""
        if not filters:
            filters = []

        if pk is not None:
            filters.append({"field": "pk", "value": pk})

        q_objects = []
        for f in filters:
            field = f.get("field")

            # Validate field exists in the dynamic model
            if not self._field_exists(field):
                logger.warning(f"Skipping filter for non-existent field: {field}")
                continue

            clause = f.get("clause", "exact")
            value = f.get("value")

            # Build Q object
            if clause == "isempty":
                q_dict = {f"{field}": None}
            else:
                q_dict = {f"{field}__{clause}": value}

            is_not = f.get("is_not", False)
            if is_not:
                q_objects.append(~Q(**q_dict))
            else:
                q_objects.append(Q(**q_dict))

        return Q(*q_objects) if q_objects else Q()

    def _build_simple_q_traditional(
        self, pk: Optional[int], filters: Optional[List[Dict[str, Any]]]
    ) -> Q:
        """Build Q object for simple filters on traditional Record model."""
        # Use existing logic for traditional records
        from common.utils.graphene.query import build_q

        return build_q(pk, filters)

    def get_available_fields(self) -> List[str]:
        """
        Get list of available fields for filtering.

        Returns:
            List of field names
        """
        if self.dynamic_model:
            return [f.name for f in self.dynamic_model._meta.fields]
        else:
            # Return standard Record model fields
            return [f.name for f in Record._meta.fields]

    def get_field_type(self, field_name: str) -> Optional[str]:
        """
        Get the type of a specific field.

        Args:
            field_name: Name of the field

        Returns:
            Field type name or None
        """
        try:
            if self.dynamic_model:
                field = self.dynamic_model._meta.get_field(field_name)
            else:
                field = Record._meta.get_field(field_name)

            return field.__class__.__name__

        except Exception:
            return None


def create_query_manager(layer: DynamicLayer) -> DynamicQueryManager:
    """
    Create a query manager for a specific layer.
    """
    return DynamicQueryManager(layer)
