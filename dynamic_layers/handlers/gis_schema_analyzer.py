"""
GIS Schema Analyzer for Dynamic Table Creation

This module analyzes GIS files to determine optimal database column types
and constraints for dynamic table creation.
"""

import logging
import re
from datetime import datetime
from typing import Any, Dict, Optional

import pandas as pd
from django.conf import settings

from common.handlers.dataset_files import DatasetFilesLoader

logger = logging.getLogger("gis_schema_analyzer")


class GISSchemaAnalyzer:
    """
    Analyzes GIS files to determine optimal database schema for dynamic tables.
    """

    # Type inference thresholds (can be overridden by settings)
    SAMPLE_SIZE = getattr(settings, "DYNAMIC_TABLE_SCHEMA_ANALYSIS_SAMPLE_SIZE", 100)
    TYPE_CONFIDENCE_THRESHOLD = getattr(
        settings, "DYNAMIC_TABLE_TYPE_CONFIDENCE_THRESHOLD", 0.8
    )
    MAX_VARCHAR_LENGTH = 255
    DEFAULT_VARCHAR_LENGTH = 100

    def __init__(self, file_path: str):
        """
        Initialize the schema analyzer with a GIS file.

        Args:
            file_path: Path to the GIS file to analyze
        """
        self.file_path = file_path
        self.loader = DatasetFilesLoader(file=file_path)
        self._sample_data = None
        self._schema = None
        # TODO : get the column name from the user
        self.geometry_column_name = None

    def analyze_file_schema(self) -> Dict[str, Dict[str, Any]]:
        """
        Analyze the GIS file and return a schema definition.

        Returns:
            Dictionary containing field definitions with Django field types
        """
        if self._schema is not None:
            return self._schema

        logger.debug(f"Analyzing schema for file: {self.file_path}")

        # Load sample data for analysis
        self._load_sample_data()

        if self._sample_data is None or self._sample_data.empty:
            logger.warning(f"No data found in file: {self.file_path}")
            return {}

        # Analyze each column
        schema = dict()
        for column in self._sample_data.columns:
            if column.lower() == self.geometry_column_name:
                continue  # Skip geometry column - handled separately

            field_definition = self._analyze_column(column)
            if field_definition:
                schema[self._sanitize_column_name(column)] = field_definition

        self._schema = schema
        logger.debug(f"Schema analysis complete. Found {len(schema)} fields.")
        return schema

    def _load_sample_data(self) -> None:
        """Load sample data from the GIS file for analysis."""
        try:
            # self._sample_data = self.loader.load_data(rows_number=self.SAMPLE_SIZE)
            self._sample_data = self.loader.load_data()
            logger.debug(f"Loaded {len(self._sample_data)} rows for analysis")
        except Exception as e:
            logger.error(f"Failed to load sample data: {e}")
            self._sample_data = pd.DataFrame()

    def _analyze_column(self, column: str) -> Optional[Dict[str, Any]]:
        """
        Analyze a single column to determine its optimal Django field type.

        Args:
            column: Column name to analyze

        Returns:
            Dictionary with field type and constraints
        """
        if column not in self._sample_data.columns:
            return None

        series = self._sample_data[column].dropna()

        if series.empty:
            # All values are null
            return {
                "type": "CharField",
                "max_length": self.DEFAULT_VARCHAR_LENGTH,
                "null": True,
                "blank": True,
            }

        # Determine the best field type
        field_type = self._infer_field_type(series)
        field_definition = self._get_field_definition(field_type, series)

        return field_definition

    def _infer_field_type(self, series: pd.Series) -> str:
        """
        Infer the most appropriate Django field type for a pandas Series.

        Args:
            series: Pandas series to analyze

        Returns:
            Django field type name
        """
        # Convert to string for analysis
        str_series = series.astype(str)

        # Test for different types in order of specificity
        type_tests = [
            ("BooleanField", self._is_boolean_series),
            ("IntegerField", self._is_integer_series),
            ("FloatField", self._is_float_series),
            ("DateField", self._is_date_series),
            ("DateTimeField", self._is_datetime_series),
            ("CharField", lambda x: True),  # Default fallback
        ]

        for field_type, test_func in type_tests:
            if test_func(str_series):
                return field_type

        return "CharField"  # Fallback

    def _is_boolean_series(self, series: pd.Series) -> bool:
        """Check if series contains boolean values."""
        boolean_values = {"true", "false", "1", "0", "yes", "no", "y", "n"}
        unique_values = set(series.str.lower().unique())
        return unique_values.issubset(boolean_values) and len(unique_values) <= 2

    def _is_integer_series(self, series: pd.Series) -> bool:
        """Check if series contains integer values."""
        try:
            # Try to convert to numeric
            numeric_series = pd.to_numeric(series, errors="coerce")
            non_null_count = numeric_series.notna().sum()

            # Must have high confidence and be whole numbers
            confidence = non_null_count / len(series)
            if confidence < self.TYPE_CONFIDENCE_THRESHOLD:
                return False

            # Check if all numeric values are integers
            numeric_values = numeric_series.dropna()
            return all(float(val).is_integer() for val in numeric_values)
        except:
            return False

    def _is_float_series(self, series: pd.Series) -> bool:
        """Check if series contains float values."""
        try:
            numeric_series = pd.to_numeric(series, errors="coerce")
            non_null_count = numeric_series.notna().sum()
            confidence = non_null_count / len(series)
            return confidence >= self.TYPE_CONFIDENCE_THRESHOLD
        except:
            return False

    def _is_date_series(self, series: pd.Series) -> bool:
        """Check if series contains date values."""
        try:
            date_series = pd.to_datetime(
                series, errors="coerce", infer_datetime_format=True
            )
            non_null_count = date_series.notna().sum()
            confidence = non_null_count / len(series)

            if confidence < self.TYPE_CONFIDENCE_THRESHOLD:
                return False

            # Check if times are all midnight (indicating date-only)
            valid_dates = date_series.dropna()
            return all(dt.time() == datetime.min.time() for dt in valid_dates)
        except:
            return False

    def _is_datetime_series(self, series: pd.Series) -> bool:
        """Check if series contains datetime values."""
        try:
            date_series = pd.to_datetime(
                series, errors="coerce", infer_datetime_format=True
            )
            non_null_count = date_series.notna().sum()
            confidence = non_null_count / len(series)
            return confidence >= self.TYPE_CONFIDENCE_THRESHOLD
        except:
            return False

    def _get_field_definition(
        self, field_type: str, series: pd.Series
    ) -> Dict[str, Any]:
        """
        Get the complete field definition for a given field type.

        Args:
            field_type: Django field type name
            series: Pandas series for constraint analysis

        Returns:
            Dictionary with the field type and constraints
        """
        definition = {"type": field_type, "null": True, "blank": True}

        if field_type == "CharField":
            max_length = self._calculate_max_length(series)
            if max_length > self.MAX_VARCHAR_LENGTH:
                definition["type"] = "TextField"
            else:
                definition["max_length"] = max_length

        return definition

    def _calculate_max_length(self, series: pd.Series) -> int:
        """Calculate appropriate max_length for CharField."""
        try:
            max_length = series.astype(str).str.len().max()

            # Add some buffer and round up
            buffered_length = int(max_length * 1.2) + 10
            return buffered_length
        except:
            return self.DEFAULT_VARCHAR_LENGTH

    def _sanitize_column_name(self, column_name: str) -> str:
        """
        Sanitize column name to be valid Python identifier and Django field name.

        Args:
            column_name: Original column name

        Returns:
            Sanitized column name
        """
        # Convert to lowercase and replace invalid characters
        sanitized = re.sub(r"[^a-zA-Z0-9_]", "_", column_name.lower())

        # Ensure it starts with a letter or underscore
        if sanitized and sanitized[0].isdigit():
            sanitized = f"field_{sanitized}"

        # Ensure it's not empty
        if not sanitized:
            sanitized = "unnamed_field"

        # Avoid Python/Django reserved words
        reserved_words = {
            "id",
            "pk",
            "class",
            "def",
            "if",
            "else",
            "for",
            "while",
            "try",
            "except",
            "import",
            "from",
            "as",
            "with",
            "lambda",
            "global",
            "nonlocal",
            "assert",
            "break",
            "continue",
            "pass",
            "return",
            "yield",
            "raise",
            "del",
            "and",
            "or",
            "not",
            "in",
            "is",
            "true",
            "false",
            "none",
        }

        if sanitized in reserved_words:
            sanitized = f"{sanitized}_field"

        return sanitized

    def get_geometry_field_info(self) -> Optional[Dict[str, Any]]:
        """
        Get information about the geometry field in the GIS file.

        Returns:
            Dictionary with geometry field information
        """
        try:
            # Load a small sample to check geometry
            sample_df = self.loader.load_data(rows_number=5)

            if self.geometry_column_name in sample_df.columns:
                # Analyze geometry types
                geom_types = set()
                for geom in sample_df["geometry"].dropna():
                    if hasattr(geom, "geom_type"):
                        geom_types.add(geom.geom_type)

                return {
                    "has_geometry": True,
                    "geometry_types": list(geom_types),
                    "srid": 4326,  # Default SRID, could be detected from file
                }
        except Exception as e:
            logger.warning(f"Could not analyze geometry field: {e}")

        return {"has_geometry": False}
