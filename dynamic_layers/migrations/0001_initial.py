# Generated by Django 3.2.25 on 2025-08-19 07:52

import django.contrib.gis.db.models.fields
import django.db.models.deletion
import django_extensions.db.fields
import jsoneditor.fields.django3_jsonfield
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('layers', '0025_update_datasets_with_layers_data'),
        ('workspaces', '0024_alter_workspace_layers_sorted_ids'),
    ]

    operations = [
        migrations.CreateModel(
            name='DynamicLayer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('key', models.SlugField(max_length=200, unique=True, verbose_name='Unique Form Key')),
                ('title', models.CharField(max_length=200, verbose_name='Layer Title')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Layer Description')),
                ('read_only', models.BooleanField(default=False, help_text='Is this layer editable or not?', verbose_name='Read Only')),
                ('status', models.CharField(choices=[('published', 'Published'), ('unpublished', 'Unpublished')], db_index=True, default='unpublished', max_length=20, verbose_name='Layer Status')),
                ('boundaries', django.contrib.gis.db.models.fields.GeometryField(blank=True, null=True, srid=4326, verbose_name='Boundaries')),
                ('location_field_mapping', jsoneditor.fields.django3_jsonfield.JSONField(blank=True, default=dict, null=True, verbose_name='Location Field Mapping')),
                ('json_schema', jsoneditor.fields.django3_jsonfield.JSONField(blank=True, help_text='Form and UI Schemas', null=True, verbose_name='JSON Schema')),
                ('web_ui_json_schema', jsoneditor.fields.django3_jsonfield.JSONField(blank=True, null=True, verbose_name='Layer Web UI JsonSchema')),
                ('data', jsoneditor.fields.django3_jsonfield.JSONField(blank=True, default=dict, help_text='Layer extra data', verbose_name='Layer Data')),
                ('records_last_modified', models.DateTimeField(blank=True, null=True, verbose_name='Records Last Modified')),
                ('filters', jsoneditor.fields.django3_jsonfield.JSONField(blank=True, default=dict, help_text='Layer filters', verbose_name='Layer Filters')),
                ('dynamic_table_name', models.CharField(blank=True, help_text='Name of the dynamically created table for this layer', max_length=100, null=True, verbose_name='Dynamic Table Name')),
                ('dataset', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='dynamic_layers', to='workspaces.dataset', verbose_name='Dataset')),
                ('slds', models.ManyToManyField(blank=True, related_name='dynamic_layers', to='layers.SLD', verbose_name='SLDs')),
                ('workspace', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dynamic_layers', to='workspaces.workspace', verbose_name='Workspace')),
            ],
            options={
                'verbose_name': 'Dynamic Layer',
                'verbose_name_plural': 'Dynamic Layers',
            },
        ),
    ]
