from django.contrib.gis.db import models
from django.utils import timezone
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel
from dynamic_models.factory import ModelFactory
from dynamic_models.models import ModelSchema
from jsoneditor.fields.django3_jsonfield import JSONField


class LayerStatusChoices(models.TextChoices):
    PUBLISHED = "published", _("Published")
    UNPUBLISHED = "unpublished", _("Unpublished")


class DynamicLayer(TimeStampedModel):
    dataset = models.ForeignKey(
        "workspaces.Dataset",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="dynamic_layers",
        verbose_name=_("Dataset"),
    )
    workspace = models.ForeignKey(
        "workspaces.Workspace",
        on_delete=models.CASCADE,
        related_name="dynamic_layers",
        verbose_name=_("Workspace"),
    )
    slds = models.ManyToManyField(
        "layers.SLD", related_name="dynamic_layers", blank=True, verbose_name=_("SLDs")
    )
    key = models.SlugField(
        max_length=200, unique=True, verbose_name=_("Unique Form Key")
    )
    title = models.CharField(max_length=200, verbose_name=_("Layer Title"))
    description = models.TextField(
        null=True, blank=True, verbose_name=_("Layer Description")
    )
    read_only = models.BooleanField(
        default=False,
        verbose_name=_("Read Only"),
        help_text=_("Is this layer editable or not?"),
    )
    status = models.CharField(
        max_length=20,
        choices=LayerStatusChoices.choices,
        default=LayerStatusChoices.UNPUBLISHED,
        db_index=True,
        verbose_name=_("Layer Status"),
    )
    boundaries = models.GeometryField(
        null=True, blank=True, verbose_name=_("Boundaries")
    )
    location_field_mapping = JSONField(
        verbose_name=_("Location Field Mapping"),
        blank=True,
        null=True,
        default=dict,
    )
    json_schema = JSONField(
        blank=True,
        null=True,
        verbose_name=_("JSON Schema"),
        help_text=_("Form and UI Schemas"),
    )
    web_ui_json_schema = JSONField(
        blank=True,
        null=True,
        verbose_name=_("Layer Web UI JsonSchema"),
    )
    data = JSONField(
        blank=True,
        default=dict,
        verbose_name=_("Layer Data"),
        help_text=_("Layer extra data"),
    )
    records_last_modified = models.DateTimeField(
        null=True, blank=True, verbose_name=_("Records Last Modified")
    )
    filters = JSONField(
        blank=True,
        default=dict,
        verbose_name=_("Layer Filters"),
        help_text=_("Layer filters"),
    )
    dynamic_table_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_("Dynamic Table Name"),
        help_text=_("Name of the dynamically created table for this layer"),
    )

    class Meta:
        verbose_name = _("Dynamic Layer")
        verbose_name_plural = _("Dynamic Layers")

    def __str__(self):
        return self.key

    @cached_property
    def records_count(self):
        # return self.records.count()
        # todo: get count from dynamic table
        pass

    def publish(self):
        self.status = LayerStatusChoices.PUBLISHED
        self.save(update_fields=["status", "modified"])

    def unpublish(self):
        self.status = LayerStatusChoices.UNPUBLISHED
        self.save(update_fields=["status", "modified"])

    def update_records_last_modified(self):
        self.records_last_modified = timezone.now()
        self.save(update_fields=["records_last_modified"])

    def is_eda_report_outdated(self, dataset):
        """
        checks if the timestamp of the most recently modified records (`records_last_modified`)
        is later than the creation time of the latest EDA report.
        """
        # todo change eda report from layer not dataset
        latest_report = dataset.eda_reports.order_by("-created").first()
        if not (latest_report and self.records_last_modified):
            return True
        return self.records_last_modified > latest_report.created

    def get_dynamic_model(self):
        """
        Get the dynamic model class for this layer.

        Returns:
            Django model class or None if no dynamic table exists
        """
        try:
            model_schema = ModelSchema.objects.get(name=self.dynamic_table_name)
            return ModelFactory(model_schema).get_model()
        except ModelSchema.DoesNotExist:
            return None

    def unregister_dynamic_model(self):
        """
        Get the dynamic model class for this layer.

        Returns:
            Django model class or None if no dynamic table exists
        """
        try:
            model_schema = ModelSchema.objects.get(name=self.dynamic_table_name)
            return ModelFactory(model_schema).unregister_model()
        except ModelSchema.DoesNotExist:
            return None

    def has_dynamic_table(self):
        """
        Check if this layer has a dynamic table.

        Returns:
            True if layer has a dynamic table, False otherwise
        """
        return bool(self.dynamic_table_name)

    def set_dynamic_table(self, table_name: str):
        """
        Set the dynamic table name for this layer.

        Args:
            table_name: Name of the dynamic table
        """
        self.dynamic_table_name = table_name
        self.save(update_fields=["dynamic_table_name", "modified"])
