"""
Dynamic GraphQL Types for Dynamic Tables

This module creates GraphQL types that automatically adapt to different
layer schemas stored in dynamic_table_attrs.
"""

import logging
from typing import Any, Dict, Optional, Type

import graphene
from dynamic_models.models import ModelSchema
from graphene import Int, ObjectType
from graphene import List as GrapheneList
from graphene_gis.scalars import GISScalar

from dynamic_layers.models import DynamicLayer

logger = logging.getLogger("dynamic_graphql_types")


class DynamicRecordInterface(graphene.Interface):
    """Interface for all dynamic record types."""

    id = graphene.Int()
    layer_id = graphene.Int()
    geometry = graphene.Field(GISScalar)
    created = graphene.DateTime()
    modified = graphene.DateTime()

    @classmethod
    def resolve_type(cls, instance, info):
        """
        Resolve the concrete type for this interface.

        This method tells GraphQL which specific type to use for each record.
        """
        try:
            # Get the layer_id from the instance
            layer_id = getattr(instance, "layer_id", None)

            if layer_id:
                # Get the dynamic type for this layer
                dynamic_type = DynamicRecordTypeRegistry.get_or_create_type(layer_id)
                if dynamic_type:
                    return dynamic_type

            # Fallback to a generic type if we can't determine the specific type
            return DynamicRecordGenericType

        except Exception as e:
            logger.error(f"Error resolving dynamic record type: {e}")
            return DynamicRecordGenericType


class DynamicRecordGenericType(ObjectType):
    """Generic fallback type for dynamic records."""

    class Meta:
        interfaces = (DynamicRecordInterface,)
        description = "Generic dynamic record type"

    # Include all interface fields
    id = graphene.Int()
    layer_id = graphene.Int()
    geometry = graphene.Field(GISScalar)
    created = graphene.DateTime()
    modified = graphene.DateTime()

    # Add a generic properties field for unknown data
    properties = graphene.JSONString(description="Record properties as JSON")

    def resolve_properties(self, info):
        """Resolve properties field by extracting all non-standard fields."""
        try:
            properties = {}
            for field_name in dir(self):
                if not field_name.startswith("_") and field_name not in [
                    "id",
                    "layer_id",
                    "geometry",
                    "created",
                    "modified",
                    "properties",
                ]:
                    value = getattr(self, field_name, None)
                    if value is not None and not callable(value):
                        properties[field_name] = value
            return properties
        except Exception:
            return {}


class DynamicRecordTypeRegistry:
    """Registry for dynamic GraphQL types."""

    _type_registry = {}

    @classmethod
    def get_or_create_type(cls, layer_id: int) -> Optional[Type[ObjectType]]:
        """
        Get or create a dynamic GraphQL type for a layer.

        Args:
            layer_id: ID of the layer

        Returns:
            GraphQL ObjectType class or None if layer doesn't use dynamic tables
        """
        # Check cache first
        if layer_id in cls._type_registry:
            return cls._type_registry[layer_id]

        try:
            # Get layer and check if it uses dynamic tables
            layer = DynamicLayer.objects.get(id=layer_id)
            # Get schema from django-dynamic-model

            try:
                model_name = f"DynamicLayer{layer_id}"
                model_schema = ModelSchema.objects.get(name=model_name)

                # Build attrs-like structure for compatibility
                attrs = {}
                for field_schema in model_schema.fields.all():
                    attrs[field_schema.name] = {
                        "field_type": field_schema.data_type,
                        "attrs": {
                            "null": field_schema.null,
                            "unique": field_schema.unique,
                            "max_length": field_schema.max_length,
                        },
                    }

                # Create dynamic type
                dynamic_type = cls._create_dynamic_type(layer_id, attrs)
            except ModelSchema.DoesNotExist:
                return None
            if dynamic_type:
                cls._type_registry[layer_id] = dynamic_type

            return dynamic_type

        except DynamicLayer.DoesNotExist:
            logger.warning(f"Layer {layer_id} not found")
            return None
        except Exception as e:
            logger.error(f"Error creating dynamic type for layer {layer_id}: {e}")
            return None

    @classmethod
    def _create_dynamic_type(
        cls, layer_id: int, attrs: Dict[str, Any]
    ) -> Optional[Type[ObjectType]]:
        """
        Create a dynamic GraphQL type from stored attributes.

        Args:
            layer_id: ID of the layer
            attrs: Stored model attributes

        Returns:
            GraphQL ObjectType class
        """
        try:
            # Base fields that all dynamic types have
            type_fields = {
                "id": graphene.Int(),
                "layer_id": graphene.Int(),
                "geometry": graphene.Field(GISScalar),
                "created": graphene.DateTime(),
                "modified": graphene.DateTime(),
            }

            # Add dynamic fields based on stored attrs
            for field_name, field_info in attrs.items():
                if field_name in ["__module__", "Meta"]:
                    continue

                if isinstance(field_info, dict) and "field_type" in field_info:
                    graphql_field = cls._django_field_to_graphql(field_info)
                    if graphql_field:
                        type_fields[field_name] = graphql_field

            # Create the GraphQL type class
            type_name = f"DynamicRecord{layer_id}Type"

            # Add Meta class for DjangoObjectType compatibility
            meta_attrs = {
                "interfaces": (DynamicRecordInterface,),
                "description": f"Dynamic record type for layer {layer_id}",
            }
            type_fields["Meta"] = type("Meta", (), meta_attrs)

            # Create the type class
            dynamic_type = type(type_name, (ObjectType,), type_fields)

            logger.debug(f"Created dynamic GraphQL type: {type_name}")
            return dynamic_type

        except Exception as e:
            logger.error(f"Error creating dynamic type for layer {layer_id}: {e}")
            return None

    @classmethod
    def _django_field_to_graphql(
        cls, field_info: Dict[str, Any]
    ) -> Optional[graphene.Field]:
        """
        Convert Django field info to GraphQL field.

        Args:
            field_info: Field information from stored attrs

        Returns:
            GraphQL field or None
        """
        field_type = field_info.get("field_type")
        field_attrs = field_info.get("attrs", {})
        is_nullable = field_attrs.get("null", True)

        # Map Django field types to GraphQL types
        type_mapping = {
            "CharField": graphene.String,
            "TextField": graphene.String,
            "IntegerField": graphene.Int,
            "BigIntegerField": graphene.Int,
            "FloatField": graphene.Float,
            "DecimalField": graphene.Float,
            "BooleanField": graphene.Boolean,
            "DateField": graphene.Date,
            "DateTimeField": graphene.DateTime,
            "TimeField": graphene.String,  # No native Time type in Graphene
            "URLField": graphene.String,
            "EmailField": graphene.String,
            "SlugField": graphene.String,
        }

        graphql_type = type_mapping.get(field_type, graphene.String)

        # Return field with appropriate nullability
        if is_nullable:
            return graphql_type()
        else:
            return graphql_type(required=True)

    @classmethod
    def clear_cache(cls):
        """Clear the type registry cache."""
        cls._type_registry.clear()

    @classmethod
    def remove_type(cls, layer_id: int):
        """Remove a specific type from cache."""
        if layer_id in cls._type_registry:
            del cls._type_registry[layer_id]


class DynamicRecordListType(ObjectType):
    """List type for dynamic records with pagination info."""

    data = GrapheneList(DynamicRecordInterface)
    count = Int()

    @classmethod
    def create_for_layer(cls, layer_id: int) -> Type[ObjectType]:
        """
        Create a dynamic list type for a specific layer.

        Args:
            layer_id: ID of the layer

        Returns:
            GraphQL ObjectType class for the list
        """
        record_type = DynamicRecordTypeRegistry.get_or_create_type(layer_id)

        if record_type:
            # Create list type with specific record type
            list_fields = {
                "data": GrapheneList(record_type),
                "count": Int(),
                "Meta": type(
                    "Meta",
                    (),
                    {"description": f"List of dynamic records for layer {layer_id}"},
                ),
            }

            list_type_name = f"DynamicRecord{layer_id}ListType"
            return type(list_type_name, (ObjectType,), list_fields)
        else:
            # Fallback to generic list type
            return cls


def get_dynamic_graphql_type(layer_id: int) -> Optional[Type[ObjectType]]:
    """
    Get the dynamic GraphQL type for a layer.

    Args:
        layer_id: ID of the layer

    Returns:
        GraphQL ObjectType class or None
    """
    return DynamicRecordTypeRegistry.get_or_create_type(layer_id)


def get_dynamic_list_type(layer_id: int) -> Type[ObjectType]:
    """
    Get the dynamic list GraphQL type for a layer.

    Args:
        layer_id: ID of the layer

    Returns:
        GraphQL ObjectType class for the list
    """
    return DynamicRecordListType.create_for_layer(layer_id)


def clear_dynamic_type_cache():
    """Clear all cached dynamic types."""
    DynamicRecordTypeRegistry.clear_cache()


def remove_dynamic_type(layer_id: int):
    """Remove a specific dynamic type from cache."""
    DynamicRecordTypeRegistry.remove_type(layer_id)


class DynamicRecordConnection(graphene.Connection):
    """Connection type for paginated dynamic records."""

    class Meta:
        node = DynamicRecordInterface

    total_count = graphene.Int()

    def resolve_total_count(self, info):
        return self.length


# Global registry instance
dynamic_type_registry = DynamicRecordTypeRegistry()
