"""
Dynamic GraphQL Resolvers

This module provides GraphQL resolvers that work with dynamic tables
without using abstract interfaces that cause runtime resolution issues.
"""

import logging
import tracemalloc
from typing import Any, Dict

from django.utils.translation import gettext_lazy as _
from dynamic_models.models import ModelSchema
from gabbro.graphene import BadRequest
from graphene import Boolean, Field, Int, List, ObjectType
from graphene_gis.scalars import JSONScalar

from common.utils import (
    FilterGroupInput,
    PageInfo,
    authentication_required,
    authorize_multiple_objects_for_user,
    organization_required,
)
from dynamic_layers.models import DynamicLayer
from dynamic_layers.schema.object_types import (
    DynamicModelType,
    DynamicRecordsListType,
    DynamicRecordType,
)
from dynamic_layers.utils.dynamic_query_manager import create_query_manager
from organizations.perms_constants import VIEW_WORKSPACE
from workspaces.models import Workspace

logger = logging.getLogger("dynamic_layers")


def measure_memory(func):
    def wrapper(*args, **kwargs):
        tracemalloc.start()

        # Take snapshot before function execution
        snapshot1 = tracemalloc.take_snapshot()

        result = func(*args, **kwargs)

        # Take snapshot after function execution
        snapshot2 = tracemalloc.take_snapshot()

        # Calculate difference
        top_stats = snapshot2.compare_to(snapshot1, "lineno")

        print("[ Top 10 differences ]")
        for stat in top_stats[:10]:
            print(stat)

        tracemalloc.stop()
        return result

    return wrapper


class Query(ObjectType):
    """Query class for dynamic records."""

    dynamic_records = Field(
        DynamicRecordsListType,
        layer_id=Int(required=True),
        org_id=Int(required=True),
        pk=Int(),
        page_info=PageInfo(),
        filter_groups=List(FilterGroupInput),
        auto_complete=Boolean(default_value=False),
        description="Query records from dynamic tables",
    )
    register_dynamic_models = Field(
        DynamicModelType,
        org_id=Int(required=True),
        workspace_id=Int(required=True),
        limit=Int(default_value=100),
        unregister=Boolean(default_value=False),
    )

    unregister_dynamic_models = Field(
        DynamicModelType,
        org_id=Int(required=True),
        workspace_id=Int(required=True),
        limit=Int(default_value=100),
    )
    layer_schema = Field(
        JSONScalar,
        layer_id=Int(required=True),
        description="Get schema information for a dynamic layer",
    )

    @staticmethod
    @authentication_required
    @organization_required
    def resolve_dynamic_records(
        root,
        info,
        layer_id,
        org_id: int,
        auto_complete=False,
        pk=None,
        page_info=None,
        filter_groups=None,
        **kwargs,
    ):
        """
        Resolve dynamic records query.

        Args:
            root: GraphQL root
            info: GraphQL info
            layer_id: ID of the layer to query
            auto_complete: Whether this is for autocomplete
            pk: Primary key filter
            page_info: Pagination info
            filter_groups: Filter groups

        Returns:
            DynamicRecordsListType with data and count
        """
        try:
            # Get user and organization from context
            user = info.context.user
            organization = info.context.organization

            # Get layer and verify permissions
            layer = DynamicLayer.objects.filter(
                workspace__organization=organization, id=layer_id
            ).first()

            if not layer:
                raise BadRequest(reason={"layer_id": _("Invalid layer_id") % {}})

            # Check permissions
            authorize_multiple_objects_for_user(
                models_objs=[organization, layer.workspace],
                perm=VIEW_WORKSPACE,
                user=user,
            )

            # Use dynamic query manager
            query_manager = create_query_manager(layer)

            queryset, total_count = query_manager.query_records(
                pk=pk,
                page_info=page_info,
                filter_groups=filter_groups,
                auto_complete=auto_complete,
            )

            # Convert queryset to DynamicRecordType instances
            dynamic_records = []
            for instance in queryset:
                dynamic_record = DynamicRecordType.from_model_instance(instance)
                dynamic_records.append(dynamic_record)

            return DynamicRecordsListType(data=dynamic_records, count=total_count)

        except BadRequest:
            # Re-raise BadRequest exceptions (they contain proper error messages)
            raise
        except Exception as e:
            logger.error(f"Error resolving dynamic records for layer {layer_id}: {e}")
            raise BadRequest(
                reason={"error": _("Failed to query dynamic records") % {}}
            )

    @staticmethod
    @authentication_required
    @organization_required
    @measure_memory
    def resolve_register_dynamic_models(
        root, info, org_id: int, workspace_id, limit: int, unregister: bool, **kwargs
    ):
        user = info.context.user
        organization = info.context.organization
        workspace: Workspace = Workspace.objects.filter(
            organization=organization, id=workspace_id
        ).first()
        if not workspace:
            raise BadRequest(reason={"workspace_id": _("Invalid workspace_id") % {}})

        layers = workspace.dynamic_layers.all()[:limit]
        logger.debug("Starting registering dynamic models")
        if unregister:
            logger.debug("Unregistering dynamic models")
            for layer in layers:
                logger.debug(
                    f"Layer ID : {layer.id}",
                )
                logger.debug(layer.get_dynamic_model())
                layer.unregister_dynamic_model()
                logger.debug(f"unregistered Layer ID : {layer.id}")
        else:
            logger.debug("Registering dynamic models")
            for layer in layers:
                logger.debug(
                    f"Layer ID : {layer.id}",
                )
                logger.debug(layer.get_dynamic_model())
        return True

    @staticmethod
    @authentication_required
    @organization_required
    @measure_memory
    def resolve_unregister_dynamic_models(
        root, info, org_id: int, workspace_id, limit: int, **kwargs
    ):
        user = info.context.user
        organization = info.context.organization
        workspace: Workspace = Workspace.objects.filter(
            organization=organization, id=workspace_id
        ).first()
        if not workspace:
            raise BadRequest(reason={"workspace_id": _("Invalid workspace_id") % {}})

        layers = workspace.dynamic_layers.all()[:limit]

        logger.debug("Starting unregistering dynamic models")
        for layer in layers:
            logger.debug(
                f"Layer ID : {layer.id}",
            )
            logger.debug(layer.unregister_dynamic_model())
        return True

    @staticmethod
    def resolve_layer_schema(root, info, layer_id, **kwargs):
        """Resolve layer schema query."""
        return get_layer_schema_info(layer_id)


def get_layer_schema_info(layer_id: int) -> Dict[str, Any]:
    """
    Get schema information for a layer.

    Args:
        layer_id: ID of the layer

    Returns:
        Dictionary with schema information
    """
    try:
        layer = DynamicLayer.objects.get(id=layer_id)

        # Get field information from django-dynamic-model
        try:
            model_name = f"DynamicLayer{layer_id}"
            model_schema = ModelSchema.objects.get(name=model_name)

            fields = {}
            for field_schema in model_schema.fields.all():
                fields[field_schema.name] = {
                    "type": field_schema.data_type,
                    "attrs": {
                        "null": field_schema.null,
                        "unique": field_schema.unique,
                        "max_length": field_schema.max_length,
                    },
                }
        except ModelSchema.DoesNotExist:
            return {}

        return {
            "layer_id": layer_id,
            "table_name": layer.dynamic_table_name,
            "fields": fields,
        }

    except DynamicLayer.DoesNotExist:
        return {}
    except Exception as e:
        logger.error(f"Error getting schema info for layer {layer_id}: {e}")
        return {}
