import graphene
from graphene import DateT<PERSON>, Field, Int, ObjectType
from graphene import List as GrapheneList
from graphene_gis.scalars import GISScalar, JSONScalar


class DynamicModelType(graphene.ObjectType):
    success = graphene.Boolean()


class DynamicRecordType(ObjectType):
    """
    A flexible record type that can represent any dynamic table record.
    Uses JSONString fields to avoid interface resolution issues.
    """

    id = Int()
    layer_id = Int()
    geometry = Field(GISScalar)
    created = DateTime()
    modified = DateTime()
    properties = Field(JSONScalar)

    @classmethod
    def from_model_instance(cls, instance):
        """
        Create a DynamicRecordType from a model instance.

        Args:
            instance: Django model instance from dynamic table

        Returns:
            DynamicRecordType instance
        """
        # Extract all properties except the base fields
        properties = {}

        for field in instance._meta.fields:
            field_name = field.name
            if field_name not in ["id", "layer_id", "geometry", "created", "modified"]:
                value = getattr(instance, field_name, None)
                if value is not None:
                    # Convert to JSON-serializable format
                    if hasattr(value, "isoformat"):  # datetime objects
                        properties[field_name] = value.isoformat()
                    else:
                        properties[field_name] = value

        return cls(
            id=instance.id,
            layer_id=instance.layer_id,
            geometry=instance.geometry,
            created=getattr(instance, "created", None),
            modified=getattr(instance, "modified", None),
            properties=properties,
        )


class DynamicRecordsListType(ObjectType):
    """List type for dynamic records."""

    data = GrapheneList(DynamicRecordType)
    count = Int()
