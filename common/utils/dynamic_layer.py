"""
Dynamic Layer Models

This module contains the base model and utilities for dynamically created
layer tables using django-dynamic-model package.
"""

import logging
from typing import Any, Dict, Type

from django.contrib.gis.db import models
from dynamic_models.models import ModelSchema

logger = logging.getLogger("dynamic_layer")


def create_dynamic_layer_model(
    layer_id: int, layer_name: str, schema: Dict[str, Dict[str, Any]]
) -> Type[models.Model]:
    """
    Create a dynamic model class for a specific layer.

    This is a convenience function that creates a dynamic model using django-dynamic-model.

    Args:
        layer_id: ID of the layer
        layer_name: Name of the layer
        schema: Schema definition from GISSchemaAnalyzer

    Returns:
        Django model class
    """
    from dynamic_layers.utils.dynamic_table_manager import DynamicTableManager

    table_manager = DynamicTableManager()
    model_class, model_name = table_manager.create_dynamic_model(
        layer_id, layer_name, schema
    )

    # django-dynamic-model handles persistence automatically
    return model_class


def drop_dynamic_layer(layer_id: int) -> bool:
    """
    Drop the dynamic table and unregister the model for a layer.

    Args:
        layer_id: ID of the layer

    Returns:
        True if successful, False otherwise
    """
    try:
        from dynamic_layers.utils.dynamic_table_manager import DynamicTableManager
        from layers.models import Layer

        # Get the layer and table name
        layer = Layer.objects.get(id=layer_id)
        table_name = getattr(layer, "dynamic_table_name", None)

        if not table_name:
            logger.warning(f"No dynamic table name found for layer {layer_id}")
            return False

        # Drop the table
        table_manager = DynamicTableManager()
        success = table_manager.drop_dynamic_table(table_name)

        if success:
            # Remove the ModelSchema (django-dynamic-model will handle cleanup)
            try:
                possible_names = [
                    f"DynamicLayer{layer_id}",
                    f"Dynamiclayer{layer_id}",
                ]

                for name in possible_names:
                    try:
                        model_schema = ModelSchema.objects.get(name=name)
                        model_schema.delete()
                        logger.debug(f"Deleted ModelSchema {name} for layer {layer_id}")
                        break
                    except ModelSchema.DoesNotExist:
                        continue

            except Exception as e:
                logger.warning(
                    f"Error cleaning up ModelSchema for layer {layer_id}: {e}"
                )

            # Clear the table name from the layer
            layer.dynamic_table_name = None
            layer.save(update_fields=["dynamic_table_name", "modified"])

        return success

    except Exception as e:
        logger.error(f"Failed to drop dynamic layer {layer_id}: {e}")
        return False
