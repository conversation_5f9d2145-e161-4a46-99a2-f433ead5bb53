import os

from django.conf import settings
from django.utils.translation import gettext_lazy as _

ALLOWED_DATASET_EXTENSIONS = ["csv", "gpkg", "geojson", "kml", "zip"]


def dataset_extension_validator(file_path):
    extension = file_path.split(".")[-1]
    if extension not in ALLOWED_DATASET_EXTENSIONS:
        raise ValueError(
            _(
                "Unsupported file format: %(extension)s. Allowed extensions are: %(allowed_extensions)s"
            )
            % {"extension": extension, "allowed_extensions": ALLOWED_DATASET_EXTENSIONS}
        )


def dataset_path(instance, filename):
    return os.path.join("media", "original_datasets", filename)


def get_owner_user(organization):
    """
    Retrieves the owner user of the organization.
    """
    return organization.acl_individuals.filter(
        rule__role__codename=settings.MAIN_OWNER_ROLE_CODENAME
    ).first()
