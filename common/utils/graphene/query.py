import datetime
import json
import typing
from enum import Enum

import graphene
from django.core.exceptions import FieldError
from django.db.models import Q, F
from django.utils.translation import gettext_lazy as _
from gabbro.graphene.exceptions import NotFound


def evaluate_boolean_string(s):
    if s.lower() == "false" or s == "0":
        return False
    return bool(s)


_conversion_methods = {
    # string conversions
    "exact": lambda x: x,
    "iexact": lambda x: x,
    "contains": lambda x: x,
    "icontains": lambda x: x,
    # numbers conversions
    "gt": float,
    "lt": float,
    "gte": float,
    "lte": float,
    "range": lambda x: json.loads(x.replace("'", '"')),
    "number": lambda x: float(x),
    # date time conversions
    "date__gt": lambda x: datetime.date.fromisoformat(x).isoformat(),
    "date__lt": lambda x: datetime.date.fromisoformat(x).isoformat(),
    "date__gte": lambda x: datetime.date.fromisoformat(x).isoformat(),
    "date__lte": lambda x: datetime.date.fromisoformat(x).isoformat(),
    "time__gt": lambda x: datetime.time.fromisoformat(x).isoformat(),
    "time__lt": lambda x: datetime.time.fromisoformat(x).isoformat(),
    "time__gte": lambda x: datetime.time.fromisoformat(x).isoformat(),
    "time__lte": lambda x: datetime.time.fromisoformat(x).isoformat(),
    # ids_ in clause conversions
    "in": lambda x: json.loads(x.replace("'", '"')),
    # is null
    "isnull": evaluate_boolean_string,
    "isempty": lambda x: None,
}


class BoundedInt(graphene.Int):
    """A custom Graphene Int that has bounded values between a min and max."""

    @staticmethod
    def parse_literal(node):
        # Convert the AST node to a Python int
        try:
            value = int(node.value)
        except ValueError:
            raise ValueError(_("Value provided is not an integer") % {})
        # Here you would include your custom validation logic, for example:
        return BoundedInt.validate(value)

    @staticmethod
    def parse_value(value):
        return BoundedInt.validate(value)

    @staticmethod
    def validate(value, min_value=0):
        """Validate the integer is within the min and max bounds."""
        if value is None:
            return None

        if min_value > value:
            raise ValueError(
                _("Value must be bigger than %(min_value)s" % {"min_value": min_value})
            )
        return value


class PageInfo(graphene.InputObjectType):
    limit = BoundedInt()
    offset = BoundedInt()
    order_by = graphene.String()


class DjangoFilterClauses(Enum):
    # text clauses
    exact = "exact"
    iexact = "iexact"
    contains = "contains"
    icontains = "icontains"

    # values in clauses
    values_in = "in"

    # numbers
    gt = "gt"
    lt = "lt"
    gte = "gte"
    lte = "lte"
    range = "range"
    number = "number"

    # dates
    date__gt = "date__gt"
    date__lt = "date__lt"
    date__gte = "date__gte"
    date__lte = "date__lte"

    # times
    time__gt = "time__gt"
    time__lt = "time__lt"
    time__gte = "time__gte"
    time__lte = "time__lte"

    # is null
    isnull = "isnull"

    # is empty
    isempty = "isempty"


class GroupTypeEnum(Enum):
    AND = "AND"
    OR = "OR"


DjangoFilterChoices = graphene.Enum.from_enum(DjangoFilterClauses)
GroupTypeChoices = graphene.Enum.from_enum(GroupTypeEnum)


class DjangoFilterInput(graphene.InputObjectType):
    field = graphene.String(required=True)
    value = graphene.String(required=True)
    clause = graphene.Field(DjangoFilterChoices)
    is_not = graphene.Boolean()


class FieldFilterInput(graphene.InputObjectType):
    """
    Individual field filter with operator, field, value, and optional negation.
    """

    op = graphene.Field(DjangoFilterChoices, required=True)
    field = graphene.String(required=True)
    value = graphene.String(required=True)
    is_not = graphene.Boolean(default_value=False)


class FilterGroupInput(graphene.InputObjectType):
    """
    Group of field filters combined with AND / OR logic.
    """

    group_type = graphene.Field(GroupTypeChoices, required=True)
    is_not = graphene.Boolean(default_value=False)
    filters = graphene.List(FieldFilterInput, required=True)


def filter_qs_paginate_with_count(qs, q: Q, page_info: typing.Dict = None):
    page_info = page_info or dict()
    limit = page_info.get("limit", 100_000)
    offset = page_info.get("offset", 0)
    order_by = "__".join(page_info.get("order_by", "").strip().split("."))
    if order_by:
        # validate if field exists in qs fields
        if order_by.startswith("-"):
            order_q = F(order_by[1:]).desc(nulls_last=True)
        else:
            order_q = F(order_by).asc(nulls_first=False)
        try:
            return (
                qs.filter(q).order_by(order_q)[offset : limit + offset],
                qs.filter(q).count(),
            )
        except FieldError:
            raise NotFound(
                reason={
                    "order_by": _("invalid %(order_by)s field name")
                    % {"order_by": order_by}
                }
            )
    return qs.filter(q)[offset : limit + offset], qs.filter(q).count()


def build_q(pk: int = None, filters: typing.List = None):
    # check if there is no filters list
    if filters is None:
        filters = list()

    # clone filters to avoid changing reference of filters in memory
    _filters = [*filters]

    # inject pk filter to return the list of one object
    if pk is not None:
        _filters.append({"field": "pk", "value": pk})

    q_objects = []
    for f in _filters:
        field = f.get("field")
        clause = f.get("clause", "exact")
        value = _conversion_methods.get(clause)(f.get("value"))
        q_dict = (
            {f"{field}": value}
            if clause == "isempty"
            else {f"{field}__{clause}": value}
        )

        # if is_not flag
        is_not = f.get("is_not", False)
        if is_not:
            q_objects.append(~Q(**q_dict))
        else:
            q_objects.append(Q(**q_dict))
    return Q(*q_objects)


def build_advanced_q(filter_groups: dict = None, pk: int = None):
    """
    Build the Django Q object from the two-level advanced filter structure.

    Args:
        filter_groups: list of filter groups
        pk: Optional primary key to add as an exact filter

    Returns:
        Django Q object

    Logic:
        - All groups are combined with AND at the top level
        - Within each group, filters are combined with the group's type (AND/OR)
        - Both groups and individual filters support negation via is_not
    """
    if filter_groups is None and pk is None:
        return Q()

    # Start with the empty Q object
    combined_q = Q()

    # Handle pk injection
    if pk is not None:
        pk_q = Q(pk__exact=pk)
        combined_q &= pk_q

    # Process advanced filters if provided
    if filter_groups:
        for group in filter_groups:
            group_q = _build_group_q(group)
            if group_q:  # Only add non-empty Q objects
                combined_q &= group_q

    return combined_q


def _build_group_q(group: dict):
    """
    Build the Q object for a single filter group.

    Args:
        group: Dictionary with 'type', 'is_not', and 'filters' keys

    Returns:
        Django Q object
    """
    if not group or not isinstance(group, dict):
        return Q()

    group_type = group.get("group_type", "AND")
    is_not = group.get("is_not", False)
    filters = group.get("filters", [])

    if not filters:
        return Q()

    # Build Q objects for all filters in the group
    filter_q_objects = []
    for filter_dict in filters:
        filter_q = _build_field_q(filter_dict)
        if filter_q:  # Only add non-empty Q objects
            filter_q_objects.append(filter_q)

    if not filter_q_objects:
        return Q()

    # Combine filters based on the group type
    if group_type == "OR":
        combined_q = filter_q_objects[0]
        for q_obj in filter_q_objects[1:]:
            combined_q |= q_obj
    else:
        combined_q = filter_q_objects[0]
        for q_obj in filter_q_objects[1:]:
            combined_q &= q_obj

    # Apply group-level negation if needed
    return ~combined_q if is_not else combined_q


def _build_field_q(filter_dict: typing.Dict):
    """
    Build the Q object for a single field filter.

    Args:
        filter_dict: Dictionary with 'op', 'field', 'value', and 'is_not' keys

    Returns:
        Django Q object
    """
    if not filter_dict or not isinstance(filter_dict, dict):
        return Q()

    op = filter_dict.get("op")
    field = filter_dict.get("field")
    value = filter_dict.get("value")
    is_not = filter_dict.get("is_not", False)

    if not any([op, field, value]):
        return Q()

    # Convert value based on the operator
    conversion_func = _conversion_methods.get(op, lambda x: x)
    converted_value = conversion_func(value)

    # Build the Q object
    if op == "isempty":
        q_dict = {f"{field}": converted_value}
    elif op == "number":
        q_dict = {f"{field}": converted_value}
    else:
        q_dict = {f"{field}__{op}": converted_value}

    q_obj = Q(**q_dict)

    # Apply field-level negation if needed
    return ~q_obj if is_not else q_obj


__all__ = [
    "DjangoFilterInput",
    "FieldFilterInput",
    "FilterGroupInput",
    "PageInfo",
    "filter_qs_paginate_with_count",
    "build_q",  # Deprecated but kept for backward compatibility
    "build_advanced_q",
]
