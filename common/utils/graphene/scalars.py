import json

from django.contrib.gis.geos import GEOSGeometry
from django.utils.translation import gettext_lazy as _
from graphene import Scalar
from shapely import Geometry
from shapely.geometry import shape, mapping


class CustomJSONScalar(Scalar):
    """
    Custom scalar to handle JSON objects and GeoDjango geometries like MultiPolygon.
    """

    @staticmethod
    def serialize(value):
        # Handle GeoDjango geometry objects (e.g., MultiPolygon)
        if isinstance(value, dict):
            return value
        elif isinstance(value, Geometry):
            return json.loads(json.dumps(mapping(shape(value))))
        if isinstance(value, GEOSGeometry):
            return json.loads(value.geojson)
        else:
            # If the value is neither a GeoDjango geometry nor a dictionary, attempt to serialize it
            try:
                return json.loads(json.dumps(value))  # Ensure it's JSON serializable
            except (TypeError, ValueError):
                raise ValueError(
                    _("Value %(value)s is not JSON serializable") % {"value": value}
                )
