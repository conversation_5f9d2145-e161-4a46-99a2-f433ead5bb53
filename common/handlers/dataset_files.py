from io import Bytes<PERSON>
from json import JSONDecodeError
from pathlib import Path
from typing import List, Union
from zipfile import ZipFile, BadZipFile

import geopandas as gpd
import pandas as pd
from django.core.files import File
from django.utils.translation import gettext_lazy as _
from fiona.io import ZipMemoryFile
from gabbro.graphene.exceptions import BadRequest

from common.handlers.kml import KMLHandler
from common.utils import (
    StorageBackend,
    parse_all_properties,
    parse_list_properties,
    safe_geojson_loader,
)
from layers.models import GeometryTypes
from workspaces.enums import LongLatEnum


class DatasetFilesLoader:
    """
    A handler class to load and sample data from various file formats.
    """

    GEOMETRY_TYPES = dict(
        Point=GeometryTypes.POINT.value,
        Polygon=GeometryTypes.POLYGON.value,
        LineString=GeometryTypes.LINESTRING.value,
        MultiPoint=GeometryTypes.GEOMETRYCOLLECTION.value,
        MultiPolygon=GeometryTypes.GEOMETRYCOLLECTION.value,
        MultiLineString=GeometryTypes.GEOMETRYCOLLECTION.value,
        GeometryCollection=GeometryTypes.GEOMETRYCOLLECTION.value,
    )

    def __init__(
        self,
        file: Union[str, BytesIO],
        columns: List[str] = None,
        excluded_columns: List[str] = None,
    ):
        """
        :param file: File path or file-like object containing the dataset.
        :param columns: List of columns to sample, If None, all columns are considered.
        :param excluded_columns: List of columns to exclude from the dataset.
        """
        self.file = file
        self.columns = columns
        self.excluded_columns = excluded_columns
        self.sample_df = None
        self.geometry_name = None
        self.geometry_type = None
        self.lat_lon_column_num: str = LongLatEnum.column.value

    def get_sample_data(self, rows_number: int) -> List[dict[str, str | list]]:
        """
        Retrieve a sample of specific rows and columns from a file.
        :param rows_number: Number of rows to sample, If None, all rows are considered.
        :return: List of dictionaries, each containing a column name and a list of sampled values.
        """
        # Retrieve a DataFrame sample
        try:
            self.sample_df = self.load_data(rows_number=rows_number)
        except Exception as e:
            raise BadRequest(reason={"error": _("Invalid dataset file") % {}})

        # Transform DataFrame into a list of dictionaries (column_name, [values])
        sample_data = [
            {"column": col, "data": parse_list_properties(self.sample_df[col].tolist())}
            for col in self.sample_df.columns
        ]
        return sample_data

    def get_all_features(self):
        """
        Retrieve all data, optionally limiting the number of rows.
        :return: List of dictionaries, each containing a column name and a list of sampled values.
        """
        # Retrieve a DataFrame of all data
        try:
            df = self.load_data()
        except Exception as e:
            raise BadRequest(
                reason={"error": _("Error loading data: %(e)s") % {"e": e}}
            )
        data = [parse_all_properties(record) for record in df.to_dict(orient="records")]
        return data

    def load_data(self, rows_number: int = None) -> pd.DataFrame:
        """
        Load a sample of data from a file with specified columns and rows, excluding columns as necessary.
        :param rows_number: Number of rows to sample, If None, all rows are considered.
        :return: DataFrame containing the sample.
        """
        with StorageBackend().open(self.file) as file_obj:
            file_extension = self._get_file_extension(file_obj)

            if file_extension == "csv":
                df = self._load_csv(file_obj, rows_number)
                self.geometry_type = self.GEOMETRY_TYPES["Point"]
                self.lat_lon_column_num = LongLatEnum.two_column.value
            elif file_extension == "gpkg":
                df = self._load_geospatial(file_obj, rows_number)
                self.geometry_name = df.geometry.name
                self.geometry_type = self.GEOMETRY_TYPES[
                    df.geometry.geom_type.unique()[0]
                ]
            elif file_extension == "zip":
                df = self._load_shapefile(file_obj, rows_number)
                self.geometry_name = df.geometry.name
                self.geometry_type = self.GEOMETRY_TYPES[
                    df.geometry.geom_type.unique()[0]
                ]
            elif file_extension == "kml":
                df = self._load_kml(file_obj, rows_number)
                self.geometry_name = df.geometry.name
            elif file_extension == "geojson":
                df = self._load_geospatial(file_obj, rows_number)
                self.geometry_name = df.geometry.name
            else:
                raise ValueError(
                    _("Unsupported file format: %(file_extension)s")
                    % {"file_extension": file_extension}
                )

            # Exclude columns if specified
            if self.excluded_columns:
                df = df.drop(columns=self.excluded_columns, errors="ignore")
        try:
            df = df.fillna("")  # Replace NaN or None with empty strings
        except Exception:
            pass
        return df

    def _load_csv(self, file_obj: BytesIO, rows_number: int = None) -> pd.DataFrame:
        if rows_number:
            return pd.read_csv(file_obj, usecols=self.columns, nrows=rows_number)
        return pd.read_csv(file_obj, usecols=self.columns)

    def _load_kml(self, file_obj: BytesIO, rows_number: int = None) -> pd.DataFrame:
        kml_handler = KMLHandler(file_obj)
        df = kml_handler.load_kml()
        if self.columns:
            df = df[self.columns]
        if rows_number:
            return df.head(rows_number)
        return df

    def _load_geospatial(self, file_obj: File, rows_number: int = None) -> pd.DataFrame:
        try:
            df = gpd.read_file(file_obj, rows=rows_number)
        except JSONDecodeError:
            # Attempt to repair malformed GeoJSON then respect rows_number
            fixed_stream = StorageBackend().open(file_obj.name)
            fixed_data = safe_geojson_loader(fixed_stream)
            df = gpd.GeoDataFrame.from_features(fixed_data.get("features", []))
            if rows_number:
                df = df.head(rows_number)
        if self.columns:
            df = df[self.columns]
        return df

    def _load_shapefile(
        self,
        file_obj: File,
        rows_number: int = None,
    ) -> pd.DataFrame:
        shp_member = get_shp_member_from_valid_zip(file_obj)
        fixed_stream = StorageBackend().open(file_obj.name)
        with ZipMemoryFile(fixed_stream) as zmf:
            with zmf.open(shp_member) as src:
                df = gpd.GeoDataFrame.from_features(src, crs=src.crs)
                if rows_number:
                    df = df.head(rows_number)
        if self.columns:
            df = df[self.columns]
        return df

    @staticmethod
    def _get_file_extension(file_obj) -> str:
        file_name = getattr(file_obj, "name", None) or "data." + "csv"
        return file_name.split(".")[-1].lower()


def validate_shapefile_zip(file_path: str) -> dict:
    """
    Validate a ZIP (stored via Django's default_storage) that must contain exactly one
    ESRI Shapefile set (.shp, .shx, .dbf). Ensures the 3 core files:
      - are in the same folder inside the ZIP,
      - are non-empty,
      - and that Fiona can open the dataset.

    Returns:
        shp_member # relative path to .shp inside the zip

    Raises:
        ShapefileZipValidationError if any validation fails.
    """

    # 1) Load the zip into memory for consistent, seekable access
    # 2) Inspect contents
    file_obj = StorageBackend().open(file_path)
    try:
        with ZipFile(file_obj) as zf:
            infos = [zi for zi in zf.infolist() if not zi.is_dir()]
            members = [zi.filename for zi in infos]
            sizes = {zi.filename: zi.file_size for zi in infos}
    except BadZipFile:
        raise Exception(_("Invalid ZIP file") % {})
    except Exception as e:
        raise Exception(_("Failed to read ZIP: %(e)s") % {"e": e})

    if not infos:
        raise Exception(_("ZIP is empty") % {})

    # 3) Group by (folder, stem) to ensure components are colocated and share the same stem
    # Use case-insensitive matching for extensions
    from collections import defaultdict

    group_exts = defaultdict(set)  # (folder, stem) -> set of extensions
    group_paths = defaultdict(dict)  # (folder, stem) -> {ext: original path}

    for name in members:
        p = Path(name)
        if p.suffix:
            folder = p.parent.as_posix()  # '' if at root
            stem = p.stem  # Path().stem per requirement
            ext = p.suffix.lstrip(".").lower()
            group_exts[(folder, stem)].add(ext)
            group_paths[(folder, stem)][ext] = name

    required = {"shp", "shx", "dbf"}

    # 4) Find candidates that have all required parts and non-empty files
    candidates = []
    for key, exts in group_exts.items():
        if required.issubset(exts):
            paths = group_paths[key]
            if all(sizes[paths[e]] > 0 for e in required):
                candidates.append((key, paths))

    if not candidates:
        found = [
            f"{('/' if not folder else folder + '/')}{stem} -> {sorted(list(exts))}"
            for (folder, stem), exts in group_exts.items()
        ]
        raise Exception(
            [
                _(
                    "No complete Shapefile found (need .shp, .shx, .dbf with same stem in same folder). \n"
                    "Found groups: %(found)s"
                )
                % {"found": "; ".join(found)},
            ]
        )

    # 5) Must contain only one shapefile set
    if len(candidates) > 1:
        stems_readable = [
            f"{folder or '.'}/{stem}" for (folder, stem), paths_map in candidates
        ]
        raise Exception(
            _(
                "ZIP contains multiple Shapefile sets; exactly one is required. \n"
                "Detected sets: %(stems_readable)s"
            )
            % {"stems_readable": ", ".join(stems_readable)}
        )

    # Single candidate
    (folder, stem), paths_map = candidates[0]
    shp_member = paths_map["shp"]

    # 6) Probe with Fiona (in-memory ZIP)
    try:
        fixed_stream = StorageBackend().open(file_obj.name)
        with ZipMemoryFile(fixed_stream) as zmf:
            with zmf.open(shp_member) as src:
                # Access minimal metadata and read at least one feature (if any)
                schema = src.schema
                for feature in src:
                    break
    except Exception as e:
        raise Exception(
            _("Fiona could not open the Shapefile inside the ZIP: %(e)s") % {"e": e}
        )

    # 7) Success
    return shp_member


def get_shp_member_from_valid_zip(file_obj: File) -> str:
    """
    Extract and return the relative path of the single .shp entry inside a ZIP
    that has already been validated by `validate_shapefile_zip`.

    Assumptions (not revalidated here):
    - The ZIP contains exactly one Shapefile set.
    """
    with ZipFile(file_obj) as zf:
        root_shps = [
            zi.filename
            for zi in zf.infolist()
            if not zi.is_dir() and Path(zi.filename).suffix.lower() == ".shp"
        ]
    if len(root_shps) != 1:
        raise Exception(
            _(
                "Unexpected ZIP contents. Expected exactly one top-level .shp entry.\n"
                "Found: %(stems)s\n"
                "Ensure `validate_shapefile_zip` is called before this function."
            )
            % {"stems": ", ".join(root_shps) or "none"}
        )
    return root_shps[0]
