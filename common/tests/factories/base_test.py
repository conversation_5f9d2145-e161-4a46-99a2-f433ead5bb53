from unittest.mock import MagicMock, patch

from django.contrib.auth.models import AnonymousUser, Permission
from django.db.models import Q
from django.test import TestCase
from django.utils.translation import activate
from graphene.test import Client

from app.schema import schema
from .organization import OrganizationFactory
from .role import RoleFactory
from .user import UserFactory


class BaseTestMixin(TestCase):
    def setUp(self):
        patcher3 = patch("organizations.models.AbstractOrganization.info")
        mock_info = patcher3.start()
        mock_info.return_value = None
        activate("en")
        self.user = UserFactory(is_superuser=False)
        self.superuser = UserFactory(
            is_superuser=True,
        )
        self.client = Client(schema)
        self.auth_request = MagicMock()
        self.super_user_auth_request = MagicMock()
        self.super_user_auth_request.user = self.superuser
        self.auth_request.user = self.user
        self.role_manager = RoleFactory(codename="Manager")
        permissions = Permission.objects.filter(
            (Q(content_type__model="organization"))
            & (
                Q(codename__endswith="_user")
                | Q(codename__endswith="_workspace")
                | Q(codename__endswith="_role")
            )
        )
        self.role_manager.permissions.set(permissions)
        self.organization = OrganizationFactory()
        self.organization.roles.add(self.role_manager)
        self.organization.acl_add_user(user=self.user, roles=[self.role_manager])
        self.non_auth_request = MagicMock()
        self.non_auth_request.user = AnonymousUser()
        self.maxDiff = None
