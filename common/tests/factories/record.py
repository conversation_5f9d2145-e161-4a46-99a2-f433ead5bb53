import factory.fuzzy
from django.contrib.auth import get_user_model
from faker import Faker as FakerClass

from layers.models import Record
from .layer import LayerFactory

User = get_user_model()
fake = FakerClass()



class RecordFactory(factory.django.DjangoModelFactory):
    layer = factory.SubFactory(LayerFactory)
    source_properties = factory.LazyFunction(
        lambda: {
            "city": fake.city(),
            "name": fake.name(),
            "poi_type": fake.random_element(elements=("schools", "hospitals", "parks")),
            "confidence": fake.random_element(elements=("High", "Medium", "Low")),
            "point_id": fake.random_int(min=1, max=1000),
            "parcel_id": fake.random_int(min=1000, max=9999),
        }
    )
    map_data = factory.LazyFunction(
        lambda: {
            "summary_field_1": fake.word(),
            "summary_field_2": fake.random_int(min=1, max=100),
        }
    )
    data = factory.LazyFunction(
        lambda: {
            "field1": fake.word(),
            "field2": fake.random_int(min=1, max=100),
            "field3": fake.boolean(),
        }
    )
    weight = factory.LazyAttribute(lambda _: fake.random_int(min=1, max=100))

    class Meta:
        model = Record
