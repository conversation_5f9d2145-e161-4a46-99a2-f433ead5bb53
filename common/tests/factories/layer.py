import factory.fuzzy
from django.contrib.auth import get_user_model
from faker import Faker as FakerClass

from layers.models import Layer
from .workspace import WorkspaceFactory

User = get_user_model()
fake = FakerClass()


class LayerFactory(factory.django.DjangoModelFactory):
    workspace = factory.SubFactory(WorkspaceFactory)
    key = factory.LazyAttribute(lambda _: fake.slug())
    title = factory.LazyAttribute(lambda _: fake.sentence(nb_words=3))
    description = factory.LazyAttribute(lambda _: fake.paragraph())
    read_only = False

    class Meta:
        model = Layer
