import factory.fuzzy
from django.contrib.auth import get_user_model
from faker import Faker as FakerClass

from chat_ai.models import Message
from .conversation import ConversationFactory

User = get_user_model()
fake = FakerClass()


class MessageFactory(factory.django.DjangoModelFactory):
    """Factory for creating Message instances for testing."""

    conversation = factory.SubFactory(ConversationFactory)
    message = factory.LazyFunction(
        lambda: {
            "user_input": fake.text(),
            "sql_query": f"SELECT * FROM layers_record WHERE layer_id = {fake.random_int(min=1, max=100)};",
            "sql_result": [],
            "final_result": fake.text(),
        }
    )

    class Meta:
        model = Message
