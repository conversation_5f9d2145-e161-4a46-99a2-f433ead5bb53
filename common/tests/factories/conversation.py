import factory.fuzzy
from django.contrib.auth import get_user_model
from faker import Faker as FakerClass

from chat_ai.models import Conversation
from .user import UserFactory

User = get_user_model()
fake = FakerClass()


class ConversationFactory(factory.django.DjangoModelFactory):
    """Factory for creating Conversation instances for testing."""

    user = factory.SubFactory(UserFactory)

    class Meta:
        model = Conversation
