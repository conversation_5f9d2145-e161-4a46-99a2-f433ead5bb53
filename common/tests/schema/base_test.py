from unittest.mock import MagicMock, patch

from django.contrib.auth.models import AnonymousUser, Permission
from django.contrib.contenttypes.models import ContentType
from django.test import TestCase
from django.utils.translation import activate
from graphene.test import Client

from app import schema
from app.settings_base import MAIN_OWNER_ROLE_CODENAME
from common.tests.factories.organization import OrganizationFactory
from common.tests.factories.role import RoleFactory
from common.tests.factories.user import UserFactory
from organizations.perms_constants import *


class BaseTestCase(TestCase):
    @classmethod
    def setUpClass(cls):
        activate("en")
        super().setUpClass()
        cls.patcher3 = patch("organizations.models.AbstractOrganization.info")
        mock_info = cls.patcher3.start()
        mock_info.return_value = None
        cls.organization = OrganizationFactory()
        content_type = ContentType.objects.get_for_model(cls.organization)
        cls.perms = {
            "view_user": Permission.objects.get(
                codename=VIEW_USER, content_type=content_type
            ),
            "add_user": Permission.objects.get(
                codename=ADD_USER, content_type=content_type
            ),
            "change_user": Permission.objects.get(
                codename=CHANGE_USER, content_type=content_type
            ),
            "delete_user": Permission.objects.get(
                codename=DELETE_USER, content_type=content_type
            ),
            "view_organization": Permission.objects.get(
                codename=VIEW_ORGANIZATION, content_type=content_type
            ),
            "change_organization": Permission.objects.get(
                codename=CHANGE_ORGANIZATION, content_type=content_type
            ),
            "view_layer": Permission.objects.get(
                codename=VIEW_LAYER, content_type=content_type
            ),
            "add_layer": Permission.objects.get(
                codename=ADD_LAYER, content_type=content_type
            ),
            "delete_layer": Permission.objects.get(
                codename=DELETE_LAYER, content_type=content_type
            ),
            "edit_layer": Permission.objects.get(
                codename=EDIT_LAYER, content_type=content_type
            ),
            "view_record": Permission.objects.get(
                codename=VIEW_RECORD, content_type=content_type
            ),
            "add_record": Permission.objects.get(
                codename=ADD_RECORD, content_type=content_type
            ),
            "delete_record": Permission.objects.get(
                codename=DELETE_RECORD, content_type=content_type
            ),
            "edit_record": Permission.objects.get(
                codename=EDIT_RECORD, content_type=content_type
            ),
            "view_workspace": Permission.objects.get(
                codename=VIEW_WORKSPACE, content_type=content_type
            ),
            "add_workspace": Permission.objects.get(
                codename=ADD_WORKSPACE, content_type=content_type
            ),
            "change_workspace": Permission.objects.get(
                codename=CHANGE_WORKSPACE, content_type=content_type
            ),
            "delete_workspace": Permission.objects.get(
                codename=DELETE_WORKSPACE, content_type=content_type
            ),
            "view_role": Permission.objects.get(
                codename=VIEW_ROLE, content_type=content_type
            ),
            "add_role": Permission.objects.get(
                codename=ADD_ROLE, content_type=content_type
            ),
            "change_role": Permission.objects.get(
                codename=CHANGE_ROLE, content_type=content_type
            ),
            "delete_role": Permission.objects.get(
                codename=DELETE_ROLE, content_type=content_type
            ),
        }
        cls.role = RoleFactory.create(permissions=[cls.perms["view_organization"]])
        cls.organization.roles.add(cls.role)
        cls.user = UserFactory.create(organization=cls.organization, roles=[cls.role])
        cls.auth_request = MagicMock()
        cls.auth_request.user = cls.user
        # Create an owner user for the organization (required by users query)
        cls.owner_role = RoleFactory.create(
            codename=MAIN_OWNER_ROLE_CODENAME, title="Main Owner"
        )
        cls.owner_user = UserFactory.create(
            organization=cls.organization, roles=[cls.owner_role]
        )
        cls.non_auth_request = MagicMock()
        cls.non_auth_request.user = AnonymousUser()
        # Create a user without VIEW_WORKSPACE permission
        cls.role_without_permission = RoleFactory.create(
            organization=cls.organization, permissions=[]
        )
        cls.user_without_permission = UserFactory.create(
            organization=cls.organization, roles=[cls.role_without_permission]
        )

        cls.auth_request_no_perm = MagicMock()
        cls.auth_request_no_perm.user = cls.user_without_permission
        cls.auth_request_no_perm.organization = cls.organization

    def setUp(self):
        self.client = Client(schema.schema)

    def tearDown(self):
        super().tearDown()
        patch.stopall()

    def _check_none_auth_error(self, response):
        self.assertDictEqual(
            response["errors"][0]["extensions"],
            {"http": {"reason": None, "status": 401, "status_text": "Unauthorized"}},
        )

    def _check_unauthorized_error(self, response):
        self.assertDictEqual(
            response["errors"][0]["extensions"],
            {
                "http": {
                    "status": 403,
                    "status_text": "Forbidden",
                    "reason": {"user": "Permission denied"},
                }
            },
        )

    def _execute_mutation(self, mutation, variables, context=None):
        """Execute a GraphQL mutation with given variables and context."""
        if context is None:
            context = self.auth_request
        return self.client.execute(mutation, variables=variables, context=context)
