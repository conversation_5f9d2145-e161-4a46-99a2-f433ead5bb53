from rest_framework import serializers

from organizations.models import Organization


class GeoServerProxySerializer(serializers.Serializer):
    SERVICE = serializers.CharField(default="WMS")
    VERSION = serializers.CharField(default="1.3.0")
    REQUEST = serializers.CharField(default="GetMap")
    LAYERS = serializers.CharField()
    ORGANIZATION = serializers.CharField()
    WIDTH = serializers.IntegerField()
    HEIGHT = serializers.IntegerField()
    BBOX = serializers.CharField()
    FORMAT = serializers.CharField()
    FORMAT_OPTIONS = serializers.CharField(required=False)
    TIME = serializers.Char<PERSON>ield(required=False)
    CRS = serializers.CharField(required=False)
    TRANSPARENT = serializers.CharField(required=False)
    CQL_FILTER = serializers.CharField(required=False)
    workspace_id = serializers.IntegerField()
    organization = serializers.PrimaryKeyRelatedField(
        queryset=Organization.objects.only("id")
    )
