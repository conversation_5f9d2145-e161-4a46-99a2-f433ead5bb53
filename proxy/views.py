import base64
from urllib.parse import urljoin

from django.conf import settings
from django.http import JsonResponse
from django.utils.translation import gettext_lazy as _
from gabbro.graphene import Forbidden
from rest_framework import status
from rest_framework.exceptions import ValidationError as BadRequest, PermissionDenied
from revproxy.views import ProxyView

from common.utils import authorize_multiple_objects_for_user
from organizations.perms_constants import VIEW_WORKSPACE
from proxy.serializers import GeoServerProxySerializer
from users.models import User
from workspaces.models import Workspace

GEOSERVER_SETTINGS = getattr(settings, "GEOSERVER_SETTINGS", {})
GEOSERVER_USERNAME = GEOSERVER_SETTINGS["AUTHORIZATION"]["USERNAME"]
GEOSERVER_PASSWORD = GEOSERVER_SETTINGS["AUTHORIZATION"]["PASSWORD"]


class GeoServerProxyView(ProxyView):
    geoserver_base_url = GEOSERVER_SETTINGS["INTERNAL_URL"]
    workspace_base_url: str = urljoin(geoserver_base_url, "geocore/")
    upstream: str = urljoin(workspace_base_url, "wms/")

    def get_request_headers(self):
        headers = super().get_request_headers()
        base64_auth_key = base64.b64encode(
            bytes(f"{GEOSERVER_USERNAME}:{GEOSERVER_PASSWORD}", encoding="utf8")
        ).decode("utf-8")
        headers.__setitem__("Authorization", f"Basic {base64_auth_key}")
        return headers

    def dispatch(self, request, path):
        user = self.request.user
        if not user.is_authenticated:
            return JsonResponse(
                data={"error": _("Authentication Failed")},
                status=status.HTTP_401_UNAUTHORIZED,
            )
        try:
            self.validate_query_params(user)
        except BadRequest as error:
            return JsonResponse(data=error.args[0], status=status.HTTP_400_BAD_REQUEST)
        except PermissionDenied as error:
            return JsonResponse(data=error.args[0], status=status.HTTP_403_FORBIDDEN)

        return super().dispatch(request, path)

    def validate_query_params(self, user: User):
        serializer = GeoServerProxySerializer(data=self.request.GET)
        serializer.is_valid(raise_exception=True)
        valid_data = serializer.validated_data
        organization = valid_data["organization"]
        workspace_id = valid_data["workspace_id"]
        workspace = Workspace.objects.filter(
            organization=organization, id=workspace_id
        ).first()
        if not workspace:
            raise BadRequest(
                {
                    "error": _("Invalid workspace_id %(workspace_id)s")
                    % {"workspace_id": workspace_id}
                }
            )

        try:
            authorize_multiple_objects_for_user(
                models_objs=[organization, workspace], perm=VIEW_WORKSPACE, user=user
            )
        except Forbidden:
            raise PermissionDenied(
                {"error": _("You are not authorized to access this workspace")}
            )
