"""
GoogleCloudStorage extensions suitable for handing Django's
Static and Media files.

Requires following settings:
MEDIA_URL, GS_MEDIA_BUCKET_NAME
STATIC_URL, GS_STATIC_BUCKET_NAME

In addition to
https://django-storages.readthedocs.io/en/latest/backends/gcloud.html
"""

from urllib.parse import urljoin

from django.conf import settings
from django.utils.translation import gettext_lazy as _
from storages.backends.gcloud import GoogleCloudStorage
from storages.utils import setting


class GoogleCloudMediaStorage(GoogleCloudStorage):
    """GoogleCloudStorage suitable for Django's Media files."""

    def __init__(self, **kwargs):
        if not settings.MEDIA_URL:
            raise Exception(_("MEDIA_URL has not been configured") % {})
        kwargs["bucket_name"] = setting("GS_MEDIA_BUCKET_NAME")
        super(GoogleCloudMediaStorage, self).__init__(**kwargs)

    def url(self, name):
        """.url that doesn't call Google."""
        return urljoin(settings.MEDIA_URL, name)


class GoogleCloudStaticStorage(GoogleCloudStorage):
    """GoogleCloudStorage suitable for Django's Static files"""

    def __init__(self, **kwargs):
        if not settings.STATIC_URL:
            raise Exception(_("STATIC_URL has not been configured") % {})
        kwargs["bucket_name"] = setting("GS_STATIC_BUCKET_NAME")
        super(GoogleCloudStaticStorage, self).__init__(**kwargs)

    def url(self, name):
        """.url that doesn't call Google."""
        return urljoin(settings.STATIC_URL, name)
