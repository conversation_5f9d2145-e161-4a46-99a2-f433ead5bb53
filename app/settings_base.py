import os  # noqa

from gabbro.settings import *  # noqa
from gabbro.settings import INSTALLED_APPS as GABBRO_INSTALLED_APPS, MIDDLEWARE

PROJECT_APPS = [
    # include gabbro for global gabbro locale messages
    "gabbro",
    # Access control list based on Django Guardian
    "gabbro.acl",
    # app to handle images and attachments
    "gabbro.uploads",
    # Commonly used models and utils apps
    "common",
    "proxy",
]
CORE_APPS = [
    "users",
    "layers",
    "organizations",
    "workspaces",
    "chat_ai",
    "dynamic_layers",
]
THIRD_PARTY_APPS = ["dynamic_models"]
INSTALLED_APPS = GABBRO_INSTALLED_APPS + PROJECT_APPS + CORE_APPS + THIRD_PARTY_APPS

MIDDLEWARE = ["django.middleware.gzip.GZipMiddleware", *MIDDLEWARE]

INTERNAL_MODE = True if os.getenv("INTERNAL_MODE", "False") == "True" else False
NOTEBOOK_ARGUMENTS = [
    "--ip",
    "0.0.0.0",
    "--port",
    "8888",
    "--allow-root",
    "--no-browser",
]

# LOGGING
LOGGING_LOGGERS_DEFAULTS = {
    "propagate": True,
    "level": "DEBUG",
}
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default_formatter": {
            "format": "%(asctime)s - [%(name)s] - %(message)s",
        },
    },
    "handlers": {
        "console": {
            "level": "DEBUG",
            "formatter": "default_formatter",
            "class": "logging.StreamHandler",
        },
        "structured_log_handler": {
            "class": "google.cloud.logging.handlers.StructuredLogHandler",
            "level": "INFO",
        },
    },
    "loggers": {
        app: {**LOGGING_LOGGERS_DEFAULTS, "handlers": ["console"]}
        for app in INSTALLED_APPS
    },
}

DATASET_SAMPLE_ROWS_NUMBER = int(os.getenv("DATASET_SAMPLE_ROWS_NUMBER", 5))
RECORDS_BATCH_SIZE = int(os.getenv("RECORDS_BATCH_SIZE", 1000))

GEOSERVER_SETTINGS = {
    "INTERNAL_URL": os.getenv(
        "GEOSERVER_INTERNAL_URL", "http://localhost:8080/geoserver/"
    ),
    "ORGANIZATIONS_WORKSPACE_NAME": os.getenv("WORKSPACE_NAME"),
    "ORGANIZATIONS_DATA_STORE_NAME": os.getenv("DATA_STORE_NAME"),
    "AUTHORIZATION": {
        "USERNAME": os.getenv("GEOSERVER_USERNAME", "admin"),
        "PASSWORD": os.getenv("GEOSERVER_PASSWORD", "geoserver"),
    },
}

WORKSPACE_ROLES_CODENAMES = ["workspace-admin", "workspace-editor", "workspace-viewer"]
MAIN_OWNER_ROLE_CODENAME = "main-owner"

# Dynamic Tables Configuration
ENABLE_DYNAMIC_TABLES = os.getenv("ENABLE_DYNAMIC_TABLES", "True")
DYNAMIC_TABLE_SCHEMA_ANALYSIS_SAMPLE_SIZE = int(
    os.getenv("DYNAMIC_TABLE_SCHEMA_ANALYSIS_SAMPLE_SIZE", 100)
)
DYNAMIC_TABLE_TYPE_CONFIDENCE_THRESHOLD = float(
    os.getenv("DYNAMIC_TABLE_TYPE_CONFIDENCE_THRESHOLD", 0.8)
)
