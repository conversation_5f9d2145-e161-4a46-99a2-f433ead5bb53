import graphene

import chat_ai.schema
import dynamic_layers.schema
import layers.schema
import organizations.schema
import users.schema
import workspaces.schema


class Query(
    workspaces.schema.Query,
    users.schema.Query,
    layers.schema.Query,
    chat_ai.schema.Query,
    organizations.schema.Query,
    dynamic_layers.schema.Query,
    graphene.ObjectType,
):
    pass


class Mutation(
    workspaces.schema.Mutation,
    layers.schema.Mutation,
    chat_ai.schema.ChatAIMutation,
    users.schema.Mutation,
    organizations.schema.Mutation,
    graphene.ObjectType,
):
    pass


schema = graphene.Schema(
    query=Query,
    mutation=Mutation,
)
