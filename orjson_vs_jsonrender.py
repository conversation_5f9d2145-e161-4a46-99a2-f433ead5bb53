import json
from datetime import datetime

import orjson
from rest_framework.renderers import JSONRenderer

data = dict(
    created=datetime.now(),
    person=dict(
        name="<PERSON>",
        age=25,
        birth_day=datetime.now(),
        sons=[dict(name="<PERSON><PERSON><PERSON>", age=2, birth_day=datetime.now())],
    ),
)

parsed_with_orjson = orjson.loads(orjson.dumps(data))
parsed_with_jsonrender = json.loads(JSONRenderer().render(data))

print(f"orjson: {parsed_with_orjson}")
print(f"jsonrender: {parsed_with_jsonrender}")
