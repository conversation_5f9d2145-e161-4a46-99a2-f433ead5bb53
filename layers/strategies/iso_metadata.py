import logging

from common.interfaces import Strategy
from layers.serializers.iso_metadata import UpdateLayerISOMetadataSerializer
from organizations.models import Organization
from users.models import User

logger = logging.getLogger("layers")


class UpdateLayerISOMetaDataStrategy(Strategy):
    def __init__(self):
        super().__init__()
        self._logger = logger

    def handle(
        self,
        context: dict,
        data_input: dict,
        user: User,
        organization: Organization,
        **kwargs,
    ):
        iso_metadata = context["iso_metadata"]
        serializer = UpdateLayerISOMetadataSerializer(
            instance=iso_metadata, data=data_input, partial=bool(iso_metadata)
        )
        serializer.is_valid()
        return serializer.save()
