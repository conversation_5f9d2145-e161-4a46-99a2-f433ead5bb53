from django.utils.translation import gettext_lazy as _
from rest_framework.exceptions import ErrorDetail

from common.tests.factories import BaseTestMixin, WorkspaceRequestFactory
from workspaces.models import WorkspaceRequestChoices, RequestTypeChoices


class TestCreateISOMetaData(BaseTestMixin):
    def setUp(self):
        super().setUp()
        # Prepare an IN_PROGRESS workspace request for the current user/org
        self.workspace_request = WorkspaceRequestFactory(
            created_by=self.user,
            status=WorkspaceRequestChoices.IN_PROGRESS,
            organization=self.organization,
            request_type=RequestTypeChoices.UPLOAD_FILE,
        )
        self.mutation = """
        mutation CreateIsoMeta($orgId: Int!, $workspaceRequestId: Int!, $title: String!, $description: String!, $datasetCreationDate: String!, $tags: [String!]!, $responsibleAuthorityName: String!, $responsibleAuthorityRole: String!, $responsibleAuthorityPhone: String!, $responsibleAuthorityEmail: String!) {
          createIsoMetadata(
            dataInput: {
              orgId: $orgId,
              workspaceRequestId: $workspaceRequestId,
              title: $title,
              description: $description,
              datasetCreationDate: $datasetCreationDate,
              tags: $tags,
              responsibleAuthorityName: $responsibleAuthorityName,
              responsibleAuthorityRole: $responsibleAuthorityRole,
              responsibleAuthorityPhone: $responsibleAuthorityPhone,
              responsibleAuthorityEmail: $responsibleAuthorityEmail
            }
          ) {
            isoMetadata
          }
        }
        """
        self.valid_variables = {
            "orgId": self.organization.id,
            "workspaceRequestId": self.workspace_request.id,
            "title": "Sample ISO Title",
            "description": "Some description about the dataset",
            "datasetCreationDate": "2024-01-15",
            "tags": ["roads", "transport"],
            "responsibleAuthorityName": "Roads Authority",
            "responsibleAuthorityRole": "Data Owner",
            "responsibleAuthorityPhone": "+966500000000",
            "responsibleAuthorityEmail": "<EMAIL>",
        }

    def test_authorization_required(self):
        response = self.client.execute(
            self.mutation, variables=self.valid_variables, context=self.non_auth_request
        )
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_create_iso_metadata_success(self):
        response = self.client.execute(
            self.mutation, variables=self.valid_variables, context=self.auth_request
        )
        self.assertNotIn("errors", response)
        iso = response["data"]["createIsoMetadata"]["isoMetadata"]
        # Ensure returned JSON contains the provided fields
        self.assertEqual(iso["title"], self.valid_variables["title"])
        self.assertEqual(iso["description"], self.valid_variables["description"])
        self.assertEqual(
            iso["dataset_creation_date"], self.valid_variables["datasetCreationDate"]
        )
        self.assertEqual(iso["tags"], self.valid_variables["tags"])
        self.assertEqual(
            iso["responsible_authority_email"],
            self.valid_variables["responsibleAuthorityEmail"],
        )

    def test_create_iso_metadata_invalid_email(self):
        variables = {**self.valid_variables, "responsibleAuthorityEmail": "invalid"}
        response = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertIn("errors", response)
        # Expect DRF serializer validation error propagated via BadRequest
        extensions = response["errors"][0]["extensions"]
        self.assertDictEqual(
            extensions,
            {
                "http": {
                    "reason": {
                        "responsibleAuthorityEmail": [
                            ErrorDetail(
                                string="Enter a valid email address.", code="invalid"
                            )
                        ]
                    },
                    "status": 400,
                    "status_text": "Bad Request",
                }
            },
        )
