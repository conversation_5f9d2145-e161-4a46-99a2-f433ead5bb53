from datetime import date

from django.utils.translation import gettext_lazy as _

from common.tests.factories import BaseTestMixin, WorkspaceFactory, LayerFactory
from layers.models import LayerISOMetadata


class TestUpdateLayerISOMetadata(BaseTestMixin):
    def setUp(self):
        super().setUp()
        self.workspace = WorkspaceFactory(
            owner=self.user, organization=self.organization
        )
        self.layer = LayerFactory(workspace=self.workspace)
        self.iso = LayerISOMetadata.objects.create(
            layer=self.layer,
            title="Old Title",
            description="Old description",
            dataset_creation_date=date(2024, 1, 1),
            tags=["roads"],
            responsible_authority_name="Authority",
            responsible_authority_role="Owner",
            responsible_authority_phone="+966500000000",
            responsible_authority_email="<EMAIL>",
        )
        self.mutation = """
        mutation UpdateIso($orgId: Int!, $layerId: Int!, $isoMetadataId: Int, $title: String, $description: String!,
         $datasetCreationDate: String!, $tags: [String!], $responsibleAuthorityName: String!, $responsibleAuthorityRole: String!,
          $responsibleAuthorityPhone: String!, $responsibleAuthorityEmail: String!) {
          updateLayerIsoMetadata(
            dataInput: {
              orgId: $orgId,
              layerId: $layerId,
              isoMetadataId: $isoMetadataId,
              title: $title,
              description: $description,
              datasetCreationDate: $datasetCreationDate,
              tags: $tags,
              responsibleAuthorityName: $responsibleAuthorityName,
              responsibleAuthorityRole: $responsibleAuthorityRole,
              responsibleAuthorityPhone: $responsibleAuthorityPhone,
              responsibleAuthorityEmail: $responsibleAuthorityEmail
            }
          ) {
            isoMetadata {
              id
              title
              description
              datasetCreationDate
              tags
              responsibleAuthorityEmail
            }
          }
        }
        """
        self.base_variables = {
            "orgId": self.organization.id,
            "layerId": self.layer.id,
            "isoMetadataId": self.iso.id,
            "title": "ISO Meta Data1",
            "description": "ISO",
            "datasetCreationDate": "2025-12-31",
            "tags": ["city", "test"],
            "responsibleAuthorityName": "responsibleAuthorityName",
            "responsibleAuthorityEmail": "<EMAIL>",
            "responsibleAuthorityPhone": "123456789",
            "responsibleAuthorityRole": "manager",
        }

    def test_authorization_required(self):
        variables = {**self.base_variables, "title": "New Title"}
        response = self.client.execute(
            self.mutation, variables=variables, context=self.non_auth_request
        )
        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_update_layer_iso_metadata_success(self):
        variables = {**self.base_variables, "title": "New Title"}
        response = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertNotIn("errors", response)
        iso = response["data"]["updateLayerIsoMetadata"]["isoMetadata"]
        self.assertEqual(iso["title"], "New Title")
        # Verify DB updated
        self.iso.refresh_from_db()
        self.assertEqual(self.iso.title, "New Title")

    def test_update_with_invalid_iso_metadata_id(self):
        variables = {**self.base_variables, "isoMetadataId": 999999, "title": "X"}
        response = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertIn("errors", response)
        extensions = response["errors"][0]["extensions"]
        self.assertIn("http", extensions)
        self.assertEqual(extensions["http"].get("status"), 400)
        reason = extensions["http"].get("reason")
        # Expect validator key to be iso_metadata_id with error message
        self.assertIn("layer", reason.keys())

    def test_update_with_iso_metadata_not_linked_to_layer(self):
        # Create another layer and ISO metadata
        other_layer = LayerFactory(workspace=self.workspace)
        other_iso = LayerISOMetadata.objects.create(
            layer=other_layer,
            title="Other",
            description="Other",
            dataset_creation_date=date(2024, 2, 2),
            tags=["x"],
            responsible_authority_name="A",
            responsible_authority_role="R",
            responsible_authority_phone="+966511111111",
            responsible_authority_email="<EMAIL>",
        )
        variables = {
            **self.base_variables,
            "isoMetadataId": other_iso.id,  # not linked to self.layer
            "title": "Try",
        }
        response = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertIn("errors", response)
        extensions = response["errors"][0]["extensions"]
        self.assertIn("http", extensions)
        self.assertEqual(extensions["http"].get("status"), 400)
        reason = extensions["http"].get("reason")
        self.assertIn("layer", reason.keys())

    def test_update_with_invalid_layer_id(self):
        variables = {**self.base_variables, "layerId": 999999, "title": "X"}
        response = self.client.execute(
            self.mutation, variables=variables, context=self.auth_request
        )
        self.assertIn("errors", response)
        extensions = response["errors"][0]["extensions"]
        self.assertIn("http", extensions)
        self.assertEqual(extensions["http"].get("status"), 400)
        reason = extensions["http"].get("reason")
        self.assertIn("layerId", reason)

    def test_update_with_invalid_responsible_authority_email(self):
        variables = {
            **self.base_variables,
            "responsibleAuthorityEmail": "invalid",
        }
        mutation = """
        mutation UpdateIso($orgId: Int!, $layerId: Int!, $isoMetadataId: Int!, $responsibleAuthorityEmail: String) {
          updateLayerIsoMetadata(
            dataInput: {
              orgId: $orgId,
              layerId: $layerId,
              isoMetadataId: $isoMetadataId,
              responsibleAuthorityEmail: $responsibleAuthorityEmail
            }
          ) {
            isoMetadata {
              id
              title
              description
              datasetCreationDate
              tags
              responsibleAuthorityEmail
            }
          }
        }
        """
        response = self.client.execute(
            mutation, variables=variables, context=self.auth_request
        )
        self.assertIn("errors", response)
        extensions = response["errors"][0]["extensions"]
        self.assertIn("http", extensions)
        self.assertEqual(extensions["http"].get("status"), 400)
        reason = extensions["http"].get("reason")
        self.assertIn("responsibleAuthorityEmail", reason)

    def test_create_iso_metadata_if_not_exists(self):
        self.iso.delete()
        self.base_variables.pop("isoMetadataId")
        response = self.client.execute(
            self.mutation, variables=self.base_variables, context=self.auth_request
        )
        self.assertNotIn("errors", response)
        iso = response["data"]["updateLayerIsoMetadata"]["isoMetadata"]
        self.assertEqual(iso["title"], self.base_variables["title"])
