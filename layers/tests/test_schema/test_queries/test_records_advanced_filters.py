"""
Test cases for records query with advanced filtering system.
"""

from django.utils.translation import gettext_lazy as _

from common.tests.factories import (
    BaseTestMixin,
    WorkspaceFactory,
    LayerFactory,
    RecordFactory,
)


class TestRecordsAdvancedFilters(BaseTestMixin):
    def setUp(self):
        super().setUp()

        # Create a workspace and layer for testing
        self.workspace = WorkspaceFactory(
            owner=self.user, organization=self.organization
        )
        self.layer = LayerFactory(workspace=self.workspace)

        # Create test records with different attributes
        self.record1 = RecordFactory(
            layer=self.layer,
            weight=25,
            source_properties={
                "city": "Riyadh",
                "name": "School A",
                "poi_type": "schools",
                "confidence": "High",
                "point_id": 1,
                "parcel_id": 1001,
            },
            map_data={"category": "education", "priority": 1},
            data={"status": "active", "capacity": 500},
        )
        self.record2 = RecordFactory(
            layer=self.layer,
            weight=75,
            source_properties={
                "city": "Jeddah",
                "name": "Hospital B",
                "poi_type": "hospitals",
                "confidence": "Medium",
                "point_id": 2,
                "parcel_id": 1002,
            },
            map_data={"category": "healthcare", "priority": 2},
            data={"status": "active", "capacity": 200},
        )
        self.record3 = RecordFactory(
            layer=self.layer,
            weight=50,
            source_properties={
                "city": "Riyadh",
                "name": "Park C",
                "poi_type": "parks",
                "confidence": "Low",
                "point_id": 3,
                "parcel_id": 1003,
            },
            map_data={"category": "recreation", "priority": 3},
            data={"status": "inactive", "capacity": 1000},
        )
        self.record4 = RecordFactory(
            layer=self.layer,
            weight=90,
            source_properties={
                "city": "Dammam",
                "name": "School D",
                "poi_type": "schools",
                "confidence": "High",
                "point_id": 4,
                "parcel_id": 1004,
            },
            map_data={"category": "education", "priority": 1},
            data={"status": "pending", "capacity": 300},
        )

        # Base GraphQL query for records
        self.query = """
        query TestRecordsQuery(
            $orgId: Int!,
            $layerId: Int!,
            $filterGroups: [FilterGroupInput!]
        ) {
          records(
            orgId: $orgId,
            layerId: $layerId,
            filterGroups: $filterGroups
          ) {
            count
            data {
              id
              weight
              sourceProperties
              mapData
              data
            }
          }
        }
        """

        self.base_variables = {
            "orgId": self.organization.id,
            "layerId": self.layer.id,
        }

    def test_query_authorization(self):
        """Test that unauthenticated users cannot access records."""
        variables = {**self.base_variables, "filterGroups": None}
        response = self.client.execute(
            self.query, variables=variables, context=self.non_auth_request
        )

        self.assertIn("errors", response)
        self.assertEqual(response["errors"][0]["message"], _("Unauthorized"))

    def test_query_without_filters(self):
        """Test basic query without any filters."""
        variables = {**self.base_variables, "filterGroups": None}
        response = self.client.execute(
            self.query, variables=variables, context=self.auth_request
        )

        self.assertNotIn("errors", response)
        data = response["data"]["records"]

        # Should return all 4 records
        self.assertEqual(data["count"], 4)

    def test_single_and_group_filter(self):
        """Test filtering with a single AND group."""
        variables = {
            **self.base_variables,
            "filterGroups": [
                {
                    "groupType": "AND",
                    "isNot": False,
                    "filters": [
                        {
                            "op": "exact",
                            "field": "source_properties__city",
                            "value": "Riyadh",
                            "isNot": False,
                        },
                        {
                            "op": "exact",
                            "field": "source_properties__poi_type",
                            "value": "schools",
                            "isNot": False,
                        },
                    ],
                }
            ],
        }

        response = self.client.execute(
            self.query, variables=variables, context=self.auth_request
        )

        self.assertNotIn("errors", response)
        data = response["data"]["records"]

        # Should return only record1 (Riyadh AND schools)
        self.assertEqual(data["count"], 1)
        self.assertEqual(data["data"][0]["id"], self.record1.id)

    def test_single_or_group_filter(self):
        """Test filtering with a single OR group."""
        variables = {
            **self.base_variables,
            "filterGroups": [
                {
                    "groupType": "OR",
                    "isNot": False,
                    "filters": [
                        {
                            "op": "exact",
                            "field": "source_properties__poi_type",
                            "value": "hospitals",
                            "isNot": False,
                        },
                        {
                            "op": "exact",
                            "field": "source_properties__poi_type",
                            "value": "parks",
                            "isNot": False,
                        },
                    ],
                }
            ],
        }

        response = self.client.execute(
            self.query, variables=variables, context=self.auth_request
        )

        self.assertNotIn("errors", response)
        data = response["data"]["records"]

        # Should return 2 records (hospitals OR parks)
        self.assertEqual(data["count"], 2)
        returned_ids = {record["id"] for record in data["data"]}
        expected_ids = {self.record2.id, self.record3.id}
        self.assertEqual(returned_ids, expected_ids)

    def test_numeric_comparison_filters(self):
        """Test numeric comparison filters (gt, gte, lt, lte)."""
        variables = {
            **self.base_variables,
            "filterGroups": [
                {
                    "groupType": "AND",
                    "isNot": False,
                    "filters": [
                        {"op": "gte", "field": "weight", "value": "50", "isNot": False},
                        {"op": "lte", "field": "weight", "value": "80", "isNot": False},
                    ],
                }
            ],
        }

        response = self.client.execute(
            self.query, variables=variables, context=self.auth_request
        )

        self.assertNotIn("errors", response)
        data = response["data"]["records"]

        # Should return 2 records (weight between 50 and 80: record2 and record3)
        self.assertEqual(data["count"], 2)
        returned_ids = {record["id"] for record in data["data"]}
        expected_ids = {self.record2.id, self.record3.id}
        self.assertEqual(returned_ids, expected_ids)

    def test_json_field_filtering(self):
        """Test filtering on JSON field properties."""
        variables = {
            **self.base_variables,
            "filterGroups": [
                {
                    "groupType": "AND",
                    "isNot": False,
                    "filters": [
                        {
                            "op": "exact",
                            "field": "data__status",
                            "value": "active",
                            "isNot": False,
                        }
                    ],
                }
            ],
        }

        response = self.client.execute(
            self.query, variables=variables, context=self.auth_request
        )

        self.assertNotIn("errors", response)
        data = response["data"]["records"]

        # Should return 2 records (status = active: record1 and record2)
        self.assertEqual(data["count"], 2)
        returned_ids = {record["id"] for record in data["data"]}
        expected_ids = {self.record1.id, self.record2.id}
        self.assertEqual(returned_ids, expected_ids)

    def test_multiple_groups_and_logic(self):
        """Test multiple filter groups combined with AND logic."""
        variables = {
            **self.base_variables,
            "filterGroups": [
                {
                    "groupType": "OR",
                    "isNot": False,
                    "filters": [
                        {
                            "op": "exact",
                            "field": "source_properties__city",
                            "value": "Riyadh",
                            "isNot": False,
                        },
                        {
                            "op": "exact",
                            "field": "source_properties__city",
                            "value": "Jeddah",
                            "isNot": False,
                        },
                    ],
                },
                {
                    "groupType": "AND",
                    "isNot": False,
                    "filters": [
                        {"op": "gt", "field": "weight", "value": "30", "isNot": False}
                    ],
                },
            ],
        }

        response = self.client.execute(
            self.query, variables=variables, context=self.auth_request
        )

        self.assertNotIn("errors", response)
        data = response["data"]["records"]

        # Should return 2 records ((Riyadh OR Jeddah) AND weight > 30: record2 and record3)
        self.assertEqual(data["count"], 2)
        returned_ids = {record["id"] for record in data["data"]}
        expected_ids = {self.record2.id, self.record3.id}
        self.assertEqual(returned_ids, expected_ids)

    def test_group_negation(self):
        """Test group-level negation."""
        variables = {
            **self.base_variables,
            "filterGroups": [
                {
                    "groupType": "OR",
                    "isNot": True,  # Negate the entire group
                    "filters": [
                        {
                            "op": "exact",
                            "field": "source_properties__poi_type",
                            "value": "schools",
                            "isNot": False,
                        }
                    ],
                }
            ],
        }

        response = self.client.execute(
            self.query, variables=variables, context=self.auth_request
        )

        self.assertNotIn("errors", response)
        data = response["data"]["records"]

        # Should return 2 records (NOT schools = hospitals and parks)
        self.assertEqual(data["count"], 2)
        returned_ids = {record["id"] for record in data["data"]}
        expected_ids = {self.record2.id, self.record3.id}
        self.assertEqual(returned_ids, expected_ids)

    def test_field_negation(self):
        """Test field-level negation."""
        variables = {
            **self.base_variables,
            "filterGroups": [
                {
                    "groupType": "AND",
                    "isNot": False,
                    "filters": [
                        {
                            "op": "exact",
                            "field": "data__status",
                            "value": "active",
                            "isNot": True,  # NOT status = active
                        }
                    ],
                }
            ],
        }

        response = self.client.execute(
            self.query, variables=variables, context=self.auth_request
        )

        self.assertNotIn("errors", response)
        data = response["data"]["records"]

        # Should return 2 records (NOT status=active = inactive and pending)
        self.assertEqual(data["count"], 2)
        returned_ids = {record["id"] for record in data["data"]}
        expected_ids = {self.record3.id, self.record4.id}
        self.assertEqual(returned_ids, expected_ids)

    def test_contains_filter(self):
        """Test text contains filtering."""
        variables = {
            **self.base_variables,
            "filterGroups": [
                {
                    "groupType": "AND",
                    "isNot": False,
                    "filters": [
                        {
                            "op": "icontains",
                            "field": "source_properties__name",
                            "value": "school",
                            "isNot": False,
                        }
                    ],
                }
            ],
        }

        response = self.client.execute(
            self.query, variables=variables, context=self.auth_request
        )

        self.assertNotIn("errors", response)
        data = response["data"]["records"]

        # Should return 2 records (names containing "school")
        self.assertEqual(data["count"], 2)
        returned_ids = {record["id"] for record in data["data"]}
        expected_ids = {self.record1.id, self.record4.id}
        self.assertEqual(returned_ids, expected_ids)

    def test_complex_nested_logic(self):
        """Test complex nested logic with multiple groups and negations."""
        variables = {
            **self.base_variables,
            "filterGroups": [
                {
                    "groupType": "OR",
                    "isNot": False,
                    "filters": [
                        {
                            "op": "exact",
                            "field": "source_properties__confidence",
                            "value": "High",
                            "isNot": False,
                        },
                        {
                            "op": "exact",
                            "field": "source_properties__confidence",
                            "value": "Medium",
                            "isNot": False,
                        },
                    ],
                },
                {
                    "groupType": "AND",
                    "isNot": True,  # NOT group
                    "filters": [
                        {
                            "op": "exact",
                            "field": "data__status",
                            "value": "pending",
                            "isNot": False,
                        }
                    ],
                },
            ],
        }

        response = self.client.execute(
            self.query, variables=variables, context=self.auth_request
        )

        self.assertNotIn("errors", response)
        data = response["data"]["records"]

        # Should return 2 records ((High OR Medium confidence) AND NOT pending status)
        # record1: High confidence, active status ✓
        # record2: Medium confidence, active status ✓
        # record4: High confidence, pending status ✗ (excluded by NOT pending)
        self.assertEqual(data["count"], 2)
        returned_ids = {record["id"] for record in data["data"]}
        expected_ids = {self.record1.id, self.record2.id}
        self.assertEqual(returned_ids, expected_ids)

    def test_empty_filter_groups(self):
        """Test behavior with empty filter groups."""
        variables = {**self.base_variables, "filterGroups": []}

        response = self.client.execute(
            self.query, variables=variables, context=self.auth_request
        )

        self.assertNotIn("errors", response)
        data = response["data"]["records"]

        # Should return all records when no filters are applied
        self.assertEqual(data["count"], 4)

    def test_backward_compatibility_with_legacy_filters(self):
        """Test that legacy filters parameter still works."""
        legacy_query = """
        query TestRecordsLegacyQuery(
            $orgId: Int!,
            $layerId: Int!,
            $filters: [DjangoFilterInput!]
        ) {
          records(
            orgId: $orgId,
            layerId: $layerId,
            filters: $filters
          ) {
            count
            data {
              id
              weight
              sourceProperties
            }
          }
        }
        """

        variables = {
            **self.base_variables,
            "filters": [
                {
                    "field": "source_properties__poi_type",
                    "value": "schools",
                    "clause": "exact",
                }
            ],
        }

        response = self.client.execute(
            legacy_query, variables=variables, context=self.auth_request
        )

        self.assertNotIn("errors", response)
        data = response["data"]["records"]

        # Should return 2 school records
        self.assertEqual(data["count"], 2)
        returned_ids = {record["id"] for record in data["data"]}
        expected_ids = {self.record1.id, self.record4.id}
        self.assertEqual(returned_ids, expected_ids)

    def test_pk_parameter_with_filter_groups(self):
        """Test pk parameter combined with filter groups."""
        query_with_pk = """
        query TestRecordsWithPk(
            $orgId: Int!,
            $layerId: Int!,
            $pk: Int!,
            $filterGroups: [FilterGroupInput!]
        ) {
          records(
            orgId: $orgId,
            layerId: $layerId,
            pk: $pk,
            filterGroups: $filterGroups
          ) {
            count
            data {
              id
              weight
            }
          }
        }
        """

        variables = {
            **self.base_variables,
            "pk": self.record1.id,
            "filterGroups": [
                {
                    "groupType": "AND",
                    "isNot": False,
                    "filters": [
                        {
                            "op": "exact",
                            "field": "source_properties__city",
                            "value": "Riyadh",
                            "isNot": False,
                        }
                    ],
                }
            ],
        }

        response = self.client.execute(
            query_with_pk, variables=variables, context=self.auth_request
        )

        self.assertNotIn("errors", response)
        data = response["data"]["records"]

        # Should return only record1 (pk filter AND city filter)
        self.assertEqual(data["count"], 1)
        self.assertEqual(data["data"][0]["id"], self.record1.id)

    def test_invalid_layer_id(self):
        """Test query with invalid layer ID."""
        variables = {
            "orgId": self.organization.id,
            "layerId": 99999,  # Non-existent layer
            "filterGroups": None,
        }

        response = self.client.execute(
            self.query, variables=variables, context=self.auth_request
        )

        self.assertIn("errors", response)
        data = {
            "errors": [
                {
                    "message": "Bad Request",
                    "locations": [{"line": 7, "column": 11}],
                    "path": ["records"],
                    "extensions": {
                        "http": {
                            "status": 400,
                            "status_text": "Bad Request",
                            "reason": {"layerId": "Invalid layer_id"},
                        }
                    },
                }
            ],
            "data": {"records": None},
        }
        self.assertEqual(response["errors"][0]["message"], "Bad Request")
        self.assertEqual(response["errors"][0]["extensions"]["http"]["status"], 400)
        self.assertEqual(
            response["errors"][0]["extensions"]["http"]["status_text"], "Bad Request"
        )
        self.assertDictEqual(
            response["errors"][0]["extensions"]["http"]["reason"],
            {"layerId": "Invalid layer_id"},
        )

    def test_filter_with_null_values(self):
        """Test filtering with null/empty values."""
        # Create a record with null weight
        record_with_null = RecordFactory(
            layer=self.layer,
            weight=None,
            source_properties={"city": "TestCity", "poi_type": "test"},
            data={"status": "test"},
        )

        variables = {
            **self.base_variables,
            "filterGroups": [
                {
                    "groupType": "AND",
                    "isNot": False,
                    "filters": [
                        {
                            "op": "isnull",
                            "field": "weight",
                            "value": "true",
                            "isNot": False,
                        }
                    ],
                }
            ],
        }

        response = self.client.execute(
            self.query, variables=variables, context=self.auth_request
        )

        self.assertNotIn("errors", response)
        data = response["data"]["records"]

        # Should return only the record with null weight
        self.assertEqual(data["count"], 1)
        self.assertEqual(data["data"][0]["id"], record_with_null.id)
