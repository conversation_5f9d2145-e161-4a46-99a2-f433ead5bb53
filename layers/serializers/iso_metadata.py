from rest_framework import serializers

from layers.models import LayerISOMetadata


class ISOMetadataSerializer(serializers.ModelSerializer):
    class Meta:
        model = LayerISOMetadata
        fields = [
            "id",
            "title",
            "description",
            "dataset_creation_date",
            "tags",
            "projection",
            "boundaries",
            "responsible_authority_name",
            "responsible_authority_role",
            "responsible_authority_phone",
            "responsible_authority_email",
        ]


class UpdateLayerISOMetadataSerializer(serializers.ModelSerializer):
    class Meta:
        model = LayerISOMetadata
        fields = [
            "id",
            "layer",
            "title",
            "description",
            "dataset_creation_date",
            "tags",
            "responsible_authority_name",
            "responsible_authority_role",
            "responsible_authority_phone",
            "responsible_authority_email",
        ]
