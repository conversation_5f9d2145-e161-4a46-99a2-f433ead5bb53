import logging

from django.conf import settings
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from gabbro.layers_engine.geoserver.catalog import (
    default_empty_metadata,
    GeoServerException,
)

from layers.gs_catalog import catalog, layer_attributes
from layers.models import SLD, Layer, SLDMapTypes

logger = logging.getLogger("layers")
not_production = settings.__getattr__("DEPLOYMENT") != "PRODUCTION"
_layer_metadata = default_empty_metadata() if not_production else None


class PublishLayerToGeoserver:
    # TODO: delete this class, the function will be in the file directly
    def __init__(
        self,
        sld: SLD,
        layer: Layer = None,
        geoserver_catalog=catalog,
    ):
        self.errors = []
        self.sld = sld
        self.layer = layer
        self.geoserver_catalog = geoserver_catalog
        self.debug = lambda method, message: logger.debug(
            f"[PublishLayerToGeoserver][{method}] {message}"
        )

    def publish(self):
        self.debug("publish", f"Publishing SLD: {self.sld.title}")
        self.publish_style()
        self.publish_layer()

    def unpublish(self):
        self.debug("unpublish", f"Unpublishing SLD: {self.sld.title}")
        self.unpublish_layer()
        self.unpublish_style()

    def publish_style(self):
        style_name = self.sld.title
        sld_attributes = self.sld.feature_style
        if self.sld.sld_type == SLDMapTypes.HEATMAP:
            sld_attributes["sld_type"] = "HEATMAP"

        try:
            self.debug("publish_style", f"Publishing Style: {style_name}")
            self.geoserver_catalog.create_sld(
                name=style_name,
                fill_color=sld_attributes.get("color", "#B22222"),
                **sld_attributes,
            )
            self.debug("publish_style", f"Style Published: {style_name}")
        except (GeoServerException, Exception) as gs_e:
            self.handle_geoserver_exception(gs_e)

    def update_style(self):
        style_name = self.sld.title
        sld_attributes = self.sld.feature_style
        if self.sld.sld_type == SLDMapTypes.HEATMAP:
            sld_attributes["sld_type"] = "HEATMAP"
        try:
            self.debug("update_style", f"Unpublishing Style: {style_name}")
            self.geoserver_catalog.update_sld(
                name=style_name,
                fill_color=sld_attributes.get("color", "#B22222"),
                **sld_attributes,
            )
            self.debug("update_style", f"Style Unpublished: {style_name}")
        except (GeoServerException, Exception) as gs_e:
            self.handle_geoserver_exception(gs_e)

    def publish_layer(self):
        if not self.layer:
            raise ValidationError(_("Layer is required") % {})

        layer_name = self.layer.key
        cql_filter = f"layer_id = {self.layer.id}"
        self.debug("publish_layer", f"Start Publishing Layer {layer_name}")
        try:
            self.geoserver_catalog.create_feature_type(
                name=layer_name,
                native_name="layers_record",
                attributes=layer_attributes,
                title=layer_name,
                cql_filter=cql_filter,
                metadata=_layer_metadata,
            )
            self.geoserver_catalog.set_layer_default_style(layer_name, self.sld.title)
            self.debug("publish_layer", f"Layer Published {layer_name}")
        except (GeoServerException, Exception) as exception:
            self.handle_geoserver_exception(exception)
        finally:
            self.layer.publish()

    def unpublish_layer(self):
        if not self.layer:
            raise ValidationError(_("Layer is required") % {})
        layer_name = self.layer.key
        try:
            status_code = self.geoserver_catalog.delete_layer(name=layer_name)
            self.debug(
                "unpublish_layer",
                f"Delete layer {layer_name}, status_code: {status_code}",
            )
            if status_code == 200:
                self.debug("unpublish_layer", f"layer {layer_name} Deleted")
                self.layer.unpublish()
        except GeoServerException as exception:
            error = exception
            self.handle_geoserver_exception(error)
        except Exception as exception:
            self.debug("unpublish_layer", f"exception: {exception}")

    def unpublish_style(self):
        style_name = self.sld.title
        try:
            status_code = self.geoserver_catalog.delete_sld(name=style_name)
            self.debug(
                "unpublish_style",
                f"Delete Style {style_name}, status_code: {status_code}",
            )
            if status_code == 200:
                self.debug("unpublish_style", f"Style {style_name} Deleted")
        except GeoServerException as exception:
            # TODO: Separate these two exceptions
            error = exception
            self.handle_geoserver_exception(error)
        except Exception as exception:
            self.debug("unpublish_style", f"exception: {exception}")

    def handle_geoserver_exception(self, exception):
        response = exception.__getattribute__("response")
        if response is not None:
            self.errors.append(
                f"GeoServer Exception {exception.__class__.__name__}: {response.status_code}, {response.content}"
            )
        else:
            self.errors.append(f"GeoServer Exception {exception}")
        self.debug(
            "handle_geoserver_exception",
            f"Error while Publishing {self.errors} / {exception}",
        )
