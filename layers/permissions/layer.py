from common.interfaces import PermissionsInterface
from common.utils import authorize_multiple_objects_for_user
from layers.models import Layer
from organizations.models import Organization
from organizations.perms_constants import (
    CHANGE_WORKSPACE,
    DELETE_WORKSPACE,
)


class UpdateLayerPermissions(PermissionsInterface):
    def check_permissions(
        self, user, organization: Organization, context: dict[str, Layer]
    ):
        layer = context["layer"]
        authorize_multiple_objects_for_user(
            models_objs=[organization, layer.workspace],
            perm=CHANGE_WORKSPACE,
            user=user,
        )


class CreateLayerFromDatasetPermissions(PermissionsInterface):
    def check_permissions(self, user, organization: Organization, context):
        workspace = context["workspace"]
        authorize_multiple_objects_for_user(
            models_objs=[organization, workspace],
            perm=CHANGE_WORKSPACE,
            user=user,
        )


class DeleteLayerPermissions(PermissionsInterface):
    def check_permissions(self, user, organization: Organization, context):
        layer = context["layer"]
        authorize_multiple_objects_for_user(
            models_objs=[organization, layer.workspace],
            perm=DELETE_WORKSPACE,
            user=user,
        )
