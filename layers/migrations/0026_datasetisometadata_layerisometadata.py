# Generated by Django 3.2 on 2025-08-20 08:33

import django.contrib.gis.db.models.fields
import django.db.models.deletion
import django_extensions.db.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("workspaces", "0027_remove_dataset_workspace"),
        ("layers", "0025_update_datasets_with_layers_data"),
    ]

    operations = [
        migrations.CreateModel(
            name="LayerISOMetadata",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                ("title", models.CharField(max_length=500, verbose_name="Title")),
                ("description", models.TextField(verbose_name="Description")),
                (
                    "dataset_creation_date",
                    models.DateField(verbose_name="Dataset Creation Date"),
                ),
                (
                    "tags",
                    models.JSONField(blank=True, default=list, verbose_name="Tags"),
                ),
                (
                    "projection",
                    models.CharField(
                        default="ESPG:4326", max_length=50, verbose_name="Projection"
                    ),
                ),
                (
                    "boundaries",
                    django.contrib.gis.db.models.fields.GeometryField(
                        blank=True, null=True, srid=4326, verbose_name="Boundaries"
                    ),
                ),
                (
                    "responsible_authority_name",
                    models.CharField(
                        max_length=500, verbose_name="Responsible Authority Name"
                    ),
                ),
                (
                    "responsible_authority_role",
                    models.TextField(verbose_name="Responsible Authority Role"),
                ),
                (
                    "responsible_authority_phone",
                    models.CharField(
                        max_length=50, verbose_name="Responsible Authority Phone"
                    ),
                ),
                (
                    "responsible_authority_email",
                    models.EmailField(
                        max_length=254, verbose_name="Responsible Authority Email"
                    ),
                ),
                (
                    "layer",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="iso_metadata",
                        to="layers.layer",
                        verbose_name="Layer",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="DatasetISOMetadata",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(
                        auto_now_add=True, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(
                        auto_now=True, verbose_name="modified"
                    ),
                ),
                ("title", models.CharField(max_length=500, verbose_name="Title")),
                ("description", models.TextField(verbose_name="Description")),
                (
                    "dataset_creation_date",
                    models.DateField(verbose_name="Dataset Creation Date"),
                ),
                (
                    "tags",
                    models.JSONField(blank=True, default=list, verbose_name="Tags"),
                ),
                (
                    "projection",
                    models.CharField(
                        default="ESPG:4326", max_length=50, verbose_name="Projection"
                    ),
                ),
                (
                    "boundaries",
                    django.contrib.gis.db.models.fields.GeometryField(
                        blank=True, null=True, srid=4326, verbose_name="Boundaries"
                    ),
                ),
                (
                    "responsible_authority_name",
                    models.CharField(
                        max_length=500, verbose_name="Responsible Authority Name"
                    ),
                ),
                (
                    "responsible_authority_role",
                    models.TextField(verbose_name="Responsible Authority Role"),
                ),
                (
                    "responsible_authority_phone",
                    models.CharField(
                        max_length=50, verbose_name="Responsible Authority Phone"
                    ),
                ),
                (
                    "responsible_authority_email",
                    models.EmailField(
                        max_length=254, verbose_name="Responsible Authority Email"
                    ),
                ),
                (
                    "dataset",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="iso_metadata",
                        to="workspaces.dataset",
                        verbose_name="Dataset",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
