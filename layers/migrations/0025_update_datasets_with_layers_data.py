# Generated by Django 3.2 on 2025-08-04 12:26

from django.db import migrations


def update_datasets_with_layers_data(apps, schema_editor):
    Dataset = apps.get_model("workspaces", "Dataset")
    datasets = Dataset.objects.filter(layers__isnull=False).select_related("layers")
    for dataset in datasets:
        layer = dataset.layers.last()
        dataset.meta_data = {
            **dataset.meta_data,
            **{
                "json_schema": layer.json_schema,
                "web_ui_json_schema": layer.web_ui_json_schema,
                "summary_fields": layer.data.get("summary_fields"),
                "location_field_mapping": layer.location_field_mapping,
                "color": layer.color,
            },
        }
    Dataset.objects.bulk_update(datasets, ["meta_data"])


class Migration(migrations.Migration):

    dependencies = [
        ("layers", "0024_layer_filters"),
    ]

    operations = []
