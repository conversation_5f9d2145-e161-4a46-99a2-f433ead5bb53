import logging
import time
from uuid import uuid4

from django.core.files.storage import default_storage
from django.core.management import BaseCommand
from django.core.management.base import CommandError

from layers.models import Layer, SLD
from workspaces.models import Workspace, Dataset

logger = logging.getLogger("layers")


class Command(BaseCommand):
    def add_arguments(self, parser):
        parser.add_argument("--workspace_id", type=int, required=True)
        parser.add_argument("--file_path", type=str, required=True)
        parser.add_argument("--batch_size", type=int, default=5000)
        parser.add_argument(
            "--replace",
            nargs="?",
            const=True,
            default=False,
            type=lambda x: str(x).lower() in ("yes", "true", "t", "y", "1"),
        )

    def handle(self, *args, **options):
        from layers.management.commands.create_layers_from_datasets import (
            validate_paths,
        )
        from layers.management.commands.load_old_tataba_data import process_layer

        start = time.time()
        logger.debug("Loading layers...")
        logger.debug("Starting ......")
        workspace_id = options.get("workspace_id")
        file_path = options.get("file_path")
        batch_size = options.get("batch_size")
        replace = options.get("replace")
        workspace: Workspace = Workspace.objects.filter(id=workspace_id).first()
        if not workspace:
            raise CommandError("The workspace_id must be a valid id")

        validate_paths([file_path])
        dataset = create_dataset_from_file(file_path)
        logger.debug(f"Dataset created {dataset.id}")
        layer = create_layer_from_dataset(dataset=dataset, workspace=workspace)
        logger.debug(f"Layer created {layer.id}")
        process_layer(layer, workspace.organization, replace, batch_size)
        end = time.time()
        logger.debug(f"Layer Loaded in {end-start} seconds")
        logger.debug("Exiting ......")


def create_dataset_from_file(file_path: str):
    dataset_file = default_storage.open(file_path)
    dataset, _ = Dataset.objects.get_or_create(file=f"{dataset_file}")
    return dataset


def create_layer_from_dataset(dataset: Dataset, workspace: Workspace):
    layer, _ = Layer.objects.get_or_create(
        workspace=workspace,
        dataset=dataset,
        defaults={"key": f"{workspace.pk} - {str(uuid4()).split('-')[0]}"},
    )
    sld, _ = SLD.objects.get_or_create(title=layer.key)
    layer.slds.add(sld)
    return layer
