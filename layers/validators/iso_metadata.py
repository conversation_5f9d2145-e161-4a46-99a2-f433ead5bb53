from django.utils.translation import gettext_lazy as _
from gabbro.graphene import BadRequest

from common.interfaces import InputValidation
from layers.models import Layer, LayerISOMetadata
from layers.serializers.iso_metadata import UpdateLayerISOMetadataSerializer
from organizations.models import Organization
from users.models import User


class UpdateLayerISOMetaDataValidator(InputValidation):
    def validate_and_get_data(
        self, data_input: dict, user: User, organization: Organization, **kwargs
    ):
        layer_id = data_input.pop("layer_id")
        iso_metadata_id = data_input.pop("iso_metadata_id", None)
        layer = Layer.objects.filter(id=layer_id).first()
        if not layer:
            raise BadRequest(
                reason={
                    "layer_id": _("Invalid layer_id: %(layer_id)s")
                    % {"layer_id": layer_id}
                }
            )

        iso_metadata = LayerISOMetadata.objects.filter(
            id=iso_metadata_id, layer=layer
        ).first()
        data_input["layer"] = layer.id
        serializer = UpdateLayerISOMetadataSerializer(
            instance=iso_metadata, data=data_input, partial=bool(iso_metadata)
        )
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)

        return dict(iso_metadata=iso_metadata, layer=layer)
