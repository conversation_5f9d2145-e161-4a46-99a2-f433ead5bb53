from typing import List

from django.utils.translation import gettext_lazy as _
from gabbro.graphene.exceptions import BadRequest

from common.interfaces import InputValidation
from common.utils import extract_json_schema_top_level_fields
from layers.mixins import LayerMixin
from layers.models import Layer
from layers.serializers import (
    LayerSerializer,
    HeatMapSerializer,
    SLDPointStyleSerializer,
)
from organizations.models import Organization
from users.models import User
from workspaces.mixins import DatasetMixin, WorkspaceMixin
from workspaces.models import RequestTypeChoices


class LayerRecordSummaryValidator(InputValidation, DatasetMixin, LayerMixin):
    def validate_and_get_data(
        self, data_input: dict, user, organization: Organization, **kwargs
    ) -> dict[str, Layer]:
        layer = self.get_layer_if_exists(data_input["layer_id"])
        workspace = layer.workspace
        if workspace.workspace_type == RequestTypeChoices.UPLOAD_FILE:
            self.validate_if_map_data_columns_included_in_dataset_sample(
                dataset=layer.dataset, map_data_columns=data_input["map_data_columns"]
            )
        if workspace.workspace_type == RequestTypeChoices.DESIGN_LAYER:
            self.validate_if_summary_fields_included_in_json_schema(
                layer=layer, map_data_columns=data_input["map_data_columns"]
            )
        return {"layer": layer}

    @staticmethod
    def validate_if_summary_fields_included_in_json_schema(
        layer: Layer, map_data_columns: List[str]
    ):
        data_columns = extract_json_schema_top_level_fields(layer.json_schema)
        if not all([c in data_columns for c in map_data_columns]):
            raise BadRequest(
                reason={
                    "error": _(
                        "columns %(columns)s not included in the layer json schema"
                    )
                    % {"columns": map_data_columns}
                }
            )


class LayerValidator(InputValidation, LayerMixin):
    def validate_and_get_data(
        self, data_input: dict, user, organization: Organization, **kwargs
    ) -> dict[str, Layer]:
        layer = self.get_layer_if_exists(data_input["layer_id"])
        serializer = LayerSerializer(data=data_input, instance=layer, partial=True)
        if not serializer.is_valid():
            raise BadRequest(reason=serializer.errors)
        return {"layer": layer}


class CreateLayerFromDatasetValidator(InputValidation, DatasetMixin, WorkspaceMixin):
    def validate_and_get_data(
        self, data_input: dict, user: User, organization: Organization, **kwargs
    ) -> dict:
        workspace = self.validate_and_get_workspace(
            data_input["workspace_id"], organization.id
        )
        dataset = self.validate_and_get_dataset(dataset_id=data_input["dataset_id"])
        return {"dataset": dataset, "workspace": workspace}


class DuplicateLayerValidator(InputValidation, LayerMixin):
    def validate_and_get_data(
        self, data_input: dict, user: User, organization: Organization, **kwargs
    ) -> dict:
        layer = self.get_layer_if_exists(data_input["layer_id"])
        return {"layer": layer}


class HeatMapValidator(InputValidation, LayerMixin):
    def validate_and_get_data(
        self, data_input: dict, user: User, organization: Organization, **kwargs
    ) -> dict:
        layer = self.get_layer_if_exists(data_input.pop("layer_id"))
        serializer = HeatMapSerializer(data=data_input)
        if not serializer.is_valid():
            raise BadRequest(serializer.errors)
        # TODO: validate if the selected weight is in the layer's columns
        return {"layer": layer, "heatmap_attributes": serializer.validated_data}


class UpdateLayerSLDValidator(InputValidation, LayerMixin):
    def validate_and_get_data(
        self, data_input: dict, user: User, organization: Organization, **kwargs
    ) -> dict:
        layer = self.get_layer_if_exists(data_input.pop("layer_id"))
        serializer = SLDPointStyleSerializer(data=data_input)
        if not serializer.is_valid():
            raise BadRequest(serializer.errors)
        return {"layer": layer}
