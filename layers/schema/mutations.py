import graphene

from common.service import Service
from common.utils import authentication_required, organization_required
from layers.permissions import (
    CreateLayerFromDatasetPermissions,
    DeleteLayerPermissions,
    UpdateLayerPermissions,
)
from layers.permissions.record import RecordPermissions
from layers.schema.input_object_types import (
    CreateLayerFromDatasetInputType,
    CreateRecordInputType,
    DeleteLayerInputType,
    DuplicateLayerInputType,
    HeatMapInputType,
    SLDInputType,
    UpdateLayerFiltersInputType,
    UpdateLayerInputType,
    UpdateRecordInputType,
    UpdateRecordsSummaryFieldsInputType,
    UpdateLayerISOMetaDataInputType,
)
from layers.schema.object_types import (
    LayerType,
    RecordType,
    SLDType,
    LayerISOMetaDataType,
)
from layers.strategies.iso_metadata import UpdateLayerISOMetaDataStrategy
from layers.strategies.layer import (
    Create<PERSON>ayer<PERSON>romDatasetStrategy,
    DeleteLayerStrategy,
    DuplicateLayerStrategy,
    LayerHeatMapStrategy,
    UpdateLayerFiltersStrategy,
    UpdateLayerSLDStrategy,
    UpdateLayerStrategy,
)
from layers.strategies.record import (
    CreateRecordsStrategy,
    UpdateRecordsSummaryFieldsStrategy,
    UpdateRecordStrategy,
)
from layers.validators import (
    CreateLayerFromDatasetValidator,
    CreateRecordValidator,
    DuplicateLayerValidator,
    HeatMapValidator,
    LayerRecordSummaryValidator,
    LayerValidator,
    UpdateLayerSLDValidator,
    UpdateRecordValidator,
)
from layers.validators.iso_metadata import UpdateLayerISOMetaDataValidator


class UpdateRecordsSummaryFields(graphene.Mutation):
    success = graphene.Boolean()

    class Input:
        data_input = UpdateRecordsSummaryFieldsInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            strategy=UpdateRecordsSummaryFieldsStrategy(),
            validator=LayerRecordSummaryValidator(),
            perms=UpdateLayerPermissions(),
        )
        service.handle(user=user, organization=organization, data_input=data_input)
        return UpdateRecordsSummaryFields(success=True)


class CreateRecord(graphene.Mutation):
    record = graphene.Field(RecordType)

    class Input:
        data_input = CreateRecordInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            strategy=CreateRecordsStrategy(),
            validator=CreateRecordValidator(),
            perms=UpdateLayerPermissions(),
        )
        record = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return CreateRecord(record=record)


class UpdateRecord(graphene.Mutation):
    record = graphene.Field(RecordType)

    class Input:
        data_input = UpdateRecordInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            strategy=UpdateRecordStrategy(),
            validator=UpdateRecordValidator(),
            perms=RecordPermissions(),
        )
        record = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return CreateRecord(record=record)


class CreateLayerFromDataset(graphene.Mutation):
    layer = graphene.Field(LayerType)

    class Input:
        data_input = CreateLayerFromDatasetInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            strategy=CreateLayerFromDatasetStrategy(),
            validator=CreateLayerFromDatasetValidator(),
            perms=CreateLayerFromDatasetPermissions(),
        )
        layer = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return CreateLayerFromDataset(layer=layer)


class UpdateLayer(graphene.Mutation):
    layer = graphene.Field(LayerType)

    class Input:
        data_input = UpdateLayerInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            strategy=UpdateLayerStrategy(),
            validator=LayerValidator(),
            perms=UpdateLayerPermissions(),
        )
        layer = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return UpdateLayer(layer=layer)


class DeleteLayer(graphene.Mutation):
    success = graphene.Boolean()

    class Input:
        data_input = DeleteLayerInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            strategy=DeleteLayerStrategy(),
            validator=LayerValidator(),
            perms=DeleteLayerPermissions(),
        )
        service.handle(user=user, organization=organization, data_input=data_input)
        return DeleteLayer(success=True)


class DuplicateLayer(graphene.Mutation):
    new_layer = graphene.Field(LayerType)

    class Input:
        data_input = DuplicateLayerInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            strategy=DuplicateLayerStrategy(),
            validator=DuplicateLayerValidator(),
            perms=UpdateLayerPermissions(),
        )
        new_layer = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return DuplicateLayer(new_layer=new_layer)


class CreateHeatMap(graphene.Mutation):
    sld = graphene.Field(SLDType)

    class Input:
        data_input = HeatMapInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            strategy=LayerHeatMapStrategy(),
            validator=HeatMapValidator(),
            perms=UpdateLayerPermissions(),
        )
        sld = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return CreateHeatMap(sld=sld)


class CreateLayerSLD(graphene.Mutation):
    sld = graphene.Field(SLDType)

    class Input:
        data_input = SLDInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            strategy=UpdateLayerSLDStrategy(),
            validator=UpdateLayerSLDValidator(),
            perms=UpdateLayerPermissions(),
        )
        sld = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return CreateLayerSLD(sld=sld)


class UpdateLayerFilters(graphene.Mutation):
    layer = graphene.Field(LayerType)

    class Input:
        data_input = UpdateLayerFiltersInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            strategy=UpdateLayerFiltersStrategy(),
            validator=LayerValidator(),
            perms=UpdateLayerPermissions(),
        )
        layer = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return UpdateLayerFilters(layer=layer)


class UpdateLayerISOMetadata(graphene.Mutation):
    iso_metadata = graphene.Field(LayerISOMetaDataType)

    class Input:
        data_input = UpdateLayerISOMetaDataInputType()

    @staticmethod
    @authentication_required
    @organization_required
    def mutate(root, info, data_input):
        user = info.context.user
        organization = info.context.organization
        service = Service(
            strategy=UpdateLayerISOMetaDataStrategy(),
            validator=UpdateLayerISOMetaDataValidator(),
            perms=UpdateLayerPermissions(),
        )
        iso_metadata = service.handle(
            user=user, organization=organization, data_input=data_input
        )
        return UpdateLayerISOMetadata(iso_metadata=iso_metadata)


class Mutation(graphene.ObjectType):
    update_records_summary_fields = UpdateRecordsSummaryFields.Field()
    create_record = CreateRecord.Field()
    update_record = UpdateRecord.Field()
    update_layer = UpdateLayer.Field()
    create_layer_from_dataset = CreateLayerFromDataset.Field()
    delete_layer = DeleteLayer.Field()
    duplicate_layer = DuplicateLayer.Field()
    create_heat_map = CreateHeatMap.Field()
    create_layer_sld = CreateLayerSLD.Field()
    update_layer_filters = UpdateLayerFilters.Field()
    update_layer_iso_metadata = UpdateLayerISOMetadata.Field()
