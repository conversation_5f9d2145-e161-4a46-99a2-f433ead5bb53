from django.contrib.gis.db import models
from django.contrib.gis.gdal.srs import CoordTransform, SpatialReference
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel
from jsoneditor.fields.django3_jsonfield import JSONField


class Record(TimeStampedModel):
    layer = models.ForeignKey(
        "layers.Layer",
        on_delete=models.CASCADE,
        null=True,
        related_name="records",
        verbose_name=_("Related Layer"),
    )
    geometry = models.GeometryField(
        null=True,
        blank=True,
        verbose_name=_("Geometry Collection Record"),
    )
    buffer_geometry = models.GeometryField(  # Add buffer field here
        null=True,
        blank=True,
        verbose_name=_("Buffer Geometry"),
    )
    source_properties = JSONField(
        default=dict,
        blank=True,
        verbose_name=_("Source Properties"),
    )
    map_data = JSONField(
        blank=True,
        null=True,
        verbose_name=_("Map Data"),
    )
    data = JSONField(
        blank=True,
        null=True,
        verbose_name=_("Order Data Dependency"),
    )
    weight = models.PositiveIntegerField(
        null=True,
        blank=True,
        verbose_name=_("Weight"),
        help_text=_("the weight values from the data field that affects on heatmap"),
    )

    class Meta:
        verbose_name = _("Geometry Record")
        verbose_name_plural = _("Geometry Records")

    def __str__(self):
        return f"LAYER: {self.layer_id}, RECORD: {self.pk}"

    def generate_buffer(self, distance):
        if not self.geometry:
            return
        # Step 1: Define the source and target CRS
        source_srs = SpatialReference(4326)  # WGS 84 (longitude, latitude)
        target_srs = SpatialReference(3857)  # Web Mercator (meters)

        # Step 2: Transform the geometry to a metric CRS
        transform_to_metric = CoordTransform(source_srs, target_srs)
        metric_geometry = self.geometry.clone()
        metric_geometry.transform(transform_to_metric)

        # Step 3: Generate the buffer in the metric CRS
        buffer_geometry_metric = metric_geometry.buffer(distance)

        # Step 4: Transform the buffer back to the original CRS (optional)
        transform_to_geographic = CoordTransform(target_srs, source_srs)
        buffer_geometry_metric.transform(transform_to_geographic)

        # Save the buffer geometry
        self.buffer_geometry = buffer_geometry_metric
        self.save(update_fields=["buffer_geometry", "modified"])
