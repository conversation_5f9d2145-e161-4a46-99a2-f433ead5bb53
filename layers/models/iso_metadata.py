from django.contrib.gis.db import models
from django.utils.translation import gettext_lazy as _
from django_extensions.db.models import TimeStampedModel


class ISOMetadataBase(TimeStampedModel):
    title = models.CharField(max_length=500, verbose_name=_("Title"))
    description = models.TextField(verbose_name=_("Description"))
    dataset_creation_date = models.DateField(verbose_name=_("Dataset Creation Date"))
    tags = models.JSONField(blank=True, default=list, verbose_name=_("Tags"))
    projection = models.Char<PERSON>ield(
        max_length=50, default="ESPG:4326", verbose_name=_("Projection")
    )
    boundaries = models.GeometryField(
        null=True, blank=True, verbose_name=_("Boundaries")
    )
    responsible_authority_name = models.CharField(
        max_length=500, verbose_name=_("Responsible Authority Name")
    )
    responsible_authority_role = models.TextField(
        verbose_name=_("Responsible Authority Role")
    )
    responsible_authority_phone = models.Char<PERSON><PERSON>(
        max_length=50, verbose_name=_("Responsible Authority Phone")
    )
    responsible_authority_email = models.EmailField(
        verbose_name=_("Responsible Authority Email")
    )

    class Meta:
        abstract = True

    def __str__(self):
        return f"{self.pk} - {self.title}"


class LayerISOMetadata(ISOMetadataBase):
    layer = models.OneToOneField(
        "layers.Layer",
        on_delete=models.CASCADE,
        related_name="iso_metadata",
        verbose_name=_("Layer"),
    )


class DatasetISOMetadata(ISOMetadataBase):
    dataset = models.OneToOneField(
        "workspaces.Dataset",
        on_delete=models.CASCADE,
        related_name="iso_metadata",
        verbose_name=_("Dataset"),
    )
